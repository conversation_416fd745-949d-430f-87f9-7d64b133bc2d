package handlers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"projectm2/internal/models"
	"projectm2/internal/services"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *services.UserService
	authService *services.AuthService
}

// NewUserHandler 创建新的用户处理器实例
func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{
		userService: services.NewUserService(db),
		authService: services.NewAuthService(db),
	}
}

// GetUsers 获取用户列表API
func (h *UserHandler) GetUsers(c *gin.Context) {
	users, err := h.userService.GetAllUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"users": users})
}

// GetActiveUsers 获取活跃用户列表API（用于任务分配）
func (h *UserHandler) GetActiveUsers(c *gin.Context) {
	users, err := h.userService.GetActiveUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"users": users})
}

// GetUserByID 根据ID获取用户API
func (h *UserHandler) GetUserByID(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"user": user})
}

// CreateUser 创建用户API（管理员创建，直接激活）
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required,min=3,max=50"`
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=6,max=100"`
		FullName string `json:"full_name" binding:"required,min=1,max=100"`
		Role     string `json:"role"`
		Avatar   string `json:"avatar"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证密码强度
	if err := h.authService.ValidatePassword(req.Password); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 创建用户请求
	createReq := models.UserCreateRequest{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
		FullName: req.FullName,
		Role:     models.UserRole(req.Role),
		Avatar:   req.Avatar,
	}

	// 使用管理员创建用户方法（直接激活）
	user, err := h.authService.CreateUserByAdmin(createReq)
	if err != nil {
		c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "用户创建成功",
		"user":    user,
	})
}

// UpdateUser 更新用户API
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req struct {
		FullName string `json:"full_name" binding:"min=1,max=100"`
		Role     string `json:"role"`
		Avatar   string `json:"avatar"`
		IsActive *bool  `json:"is_active,omitempty"` // 使用指针，可选字段
		Password string `json:"password,omitempty"`  // 可选的密码字段
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新用户请求
	updateReq := models.UserUpdateRequest{
		FullName: req.FullName,
		Role:     models.UserRole(req.Role),
		Avatar:   req.Avatar,
		IsActive: req.IsActive,
		Password: req.Password, // 传递密码字段
	}

	user, err := h.userService.UpdateUser(userID, updateReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新用户失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "用户更新成功",
		"user":    user,
	})
}

// DeleteUser 删除用户API
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	err := h.userService.DeleteUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除用户失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户删除成功"})
}

// GetUserTaskStats 获取用户任务统计API
func (h *UserHandler) GetUserTaskStats(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	stats, err := h.userService.GetUserTaskStats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"statistics": stats})
}

// ActivateUser 激活用户API
func (h *UserHandler) ActivateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	err := h.userService.ActivateUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "激活用户失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户激活成功"})
}

// DeactivateUser 停用用户API
func (h *UserHandler) DeactivateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	err := h.userService.DeactivateUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "停用用户失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户停用成功"})
}

// UploadAvatar 上传用户头像API
func (h *UserHandler) UploadAvatar(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("avatar")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "获取头像文件失败"})
		return
	}

	// 验证文件类型（只允许图片）
	allowedTypes := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp"}
	ext := strings.ToLower(filepath.Ext(file.Filename))
	isValidType := false
	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "只支持 JPG、PNG、GIF、BMP 格式的图片"})
		return
	}

	// 验证文件大小（限制为5MB）
	maxSize := int64(5 * 1024 * 1024) // 5MB
	if file.Size > maxSize {
		c.JSON(http.StatusBadRequest, gin.H{"error": "头像文件大小不能超过5MB"})
		return
	}

	// 创建头像存储目录
	avatarDir := "uploads/avatars"
	if err := os.MkdirAll(avatarDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建头像目录失败"})
		return
	}

	// 生成唯一文件名
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	filePath := filepath.Join(avatarDir, fileName)

	// 保存文件
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "保存头像文件失败"})
		return
	}

	// 生成头像URL（相对路径）
	avatarURL := fmt.Sprintf("/uploads/avatars/%s", fileName)

	c.JSON(http.StatusOK, gin.H{
		"message":    "头像上传成功",
		"avatar_url": avatarURL,
	})
}

// GetPendingUsers 获取待审核用户列表API
func (h *UserHandler) GetPendingUsers(c *gin.Context) {
	users, err := h.userService.GetPendingUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取待审核用户列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"users": users})
}

// GetUsersByStatus 根据状态获取用户列表API
func (h *UserHandler) GetUsersByStatus(c *gin.Context) {
	status := c.Query("status")
	if status == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少状态参数"})
		return
	}

	// 验证状态值
	var userStatus models.UserStatus
	switch status {
	case "pending":
		userStatus = models.StatusPending
	case "active":
		userStatus = models.StatusActive
	case "rejected":
		userStatus = models.StatusRejected
	case "disabled":
		userStatus = models.StatusDisabled
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的状态值"})
		return
	}

	users, err := h.userService.GetUsersByStatus(userStatus)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"users": users})
}

// ApproveUser 批准用户API
func (h *UserHandler) ApproveUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少用户ID"})
		return
	}

	err := h.userService.ApproveUser(userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户审核通过",
	})
}

// RejectUser 拒绝用户API
func (h *UserHandler) RejectUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少用户ID"})
		return
	}

	err := h.userService.RejectUser(userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户申请已拒绝",
	})
}

// DisableUser 禁用用户API
func (h *UserHandler) DisableUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少用户ID"})
		return
	}

	err := h.userService.DisableUser(userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户已禁用",
	})
}

// EnableUser 启用用户API
func (h *UserHandler) EnableUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少用户ID"})
		return
	}

	err := h.userService.EnableUser(userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户已启用",
	})
}

// GetUserStatusCounts 获取用户状态统计API
func (h *UserHandler) GetUserStatusCounts(c *gin.Context) {
	counts, err := h.userService.GetUserStatusCounts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"counts": counts})
}
