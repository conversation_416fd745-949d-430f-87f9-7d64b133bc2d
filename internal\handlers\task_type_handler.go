package handlers

import (
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
)

// TaskTypeHandler 任务类型处理器
type TaskTypeHandler struct {
	taskTypeService *services.TaskTypeService
}

// NewTaskTypeHandler 创建新的任务类型处理器
func NewTaskTypeHandler(taskTypeService *services.TaskTypeService) *TaskTypeHandler {
	return &TaskTypeHandler{
		taskTypeService: taskTypeService,
	}
}

// GetTaskTypes 获取所有任务类型API
func (h *TaskTypeHandler) GetTaskTypes(c *gin.Context) {
	taskTypes, err := h.taskTypeService.GetAllTaskTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务类型失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task_types": taskTypes})
}

// GetTaskTypeByID 根据ID获取任务类型API
func (h *TaskTypeHandler) GetTaskTypeByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务类型ID不能为空"})
		return
	}

	taskType, err := h.taskTypeService.GetTaskTypeByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务类型不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task_type": taskType})
}

// CreateTaskType 创建任务类型API
func (h *TaskTypeHandler) CreateTaskType(c *gin.Context) {
	var req models.TaskTypeCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	taskType, err := h.taskTypeService.CreateTaskType(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":   "任务类型创建成功",
		"task_type": taskType,
	})
}

// UpdateTaskType 更新任务类型API
func (h *TaskTypeHandler) UpdateTaskType(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务类型ID不能为空"})
		return
	}

	var req models.TaskTypeUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	taskType, err := h.taskTypeService.UpdateTaskType(id, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "任务类型更新成功",
		"task_type": taskType,
	})
}

// DeleteTaskType 删除任务类型API
func (h *TaskTypeHandler) DeleteTaskType(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务类型ID不能为空"})
		return
	}

	err := h.taskTypeService.DeleteTaskType(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务类型删除成功"})
}

// GetTaskTypeStats 获取任务类型统计信息API
func (h *TaskTypeHandler) GetTaskTypeStats(c *gin.Context) {
	stats, err := h.taskTypeService.GetTaskTypeStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计信息失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}
