package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CustomDate 自定义日期类型，支持多种日期格式
type CustomDate struct {
	time.Time
}

// UnmarshalJSON 自定义JSON解析，支持日期格式
func (cd *CustomDate) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// 移除引号
	str := string(data)
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}

	if str == "" {
		return nil
	}

	// 尝试解析日期格式 YYYY-MM-DD
	if t, err := time.Parse("2006-01-02", str); err == nil {
		cd.Time = t
		return nil
	}

	// 尝试解析完整时间格式
	if t, err := time.Parse(time.RFC3339, str); err == nil {
		cd.Time = t
		return nil
	}

	return nil
}

// MarshalJSON 自定义JSON序列化
func (cd CustomDate) MarshalJSON() ([]byte, error) {
	if cd.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + cd.Time.Format("2006-01-02") + `"`), nil
}

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	StatusTodo       TaskStatus = "todo"
	StatusInProgress TaskStatus = "in_progress"
	StatusDone       TaskStatus = "done"
)

// TaskPriority 任务优先级枚举
type TaskPriority string

const (
	PriorityLow    TaskPriority = "low"
	PriorityMedium TaskPriority = "medium"
	PriorityHigh   TaskPriority = "high"
)

// TaskType 任务类型
type TaskType string

const (
	TypeFeature       TaskType = "feature"       // 功能开发
	TypeBug           TaskType = "bug"           // 缺陷修复
	TypeTask          TaskType = "task"          // 一般任务
	TypeImprovement   TaskType = "improvement"   // 改进优化
	TypeResearch      TaskType = "research"      // 研究调研
	TypeTesting       TaskType = "testing"       // 测试
	TypeDocumentation TaskType = "documentation" // 文档
	TypeMaintenance   TaskType = "maintenance"   // 维护
)

// Task 任务模型
type Task struct {
	ID             string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Title          string         `json:"title" gorm:"not null;size:255"`
	Description    string         `json:"description" gorm:"type:text"`
	Status         TaskStatus     `json:"status" gorm:"not null;default:'todo'"`
	Priority       TaskPriority   `json:"priority" gorm:"not null;default:'medium'"`
	Type           TaskType       `json:"type" gorm:"not null;default:'task'"`
	VersionID      *string        `json:"version_id" gorm:"type:varchar(36);index"`
	Position       int            `json:"position" gorm:"not null;default:0"`
	Tags           string         `json:"tags" gorm:"type:text"` // JSON格式存储标签
	DueDate        *time.Time     `json:"due_date"`
	StartDate      *time.Time     `json:"start_date"`
	EndDate        *time.Time     `json:"end_date"`
	EstimatedHours int            `json:"estimated_hours" gorm:"default:0"`
	ActualHours    int            `json:"actual_hours" gorm:"default:0"`
	ParentTaskID   *string        `json:"parent_task_id" gorm:"type:varchar(36)"`
	AssignedTo     *string        `json:"assigned_to" gorm:"type:varchar(36);index"`
	CreatedBy      *string        `json:"created_by" gorm:"type:varchar(36);index"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	ParentTask   *Task        `json:"parent_task,omitempty" gorm:"foreignKey:ParentTaskID"`
	SubTasks     []Task       `json:"sub_tasks,omitempty" gorm:"foreignKey:ParentTaskID"`
	Comments     []Comment    `json:"comments,omitempty" gorm:"foreignKey:TaskID"`
	Attachments  []Attachment `json:"attachments,omitempty" gorm:"foreignKey:TaskID"`
	AssignedUser *User        `json:"assigned_user,omitempty" gorm:"foreignKey:AssignedTo"`
	Creator      *User        `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	Version      *TaskVersion `json:"version,omitempty" gorm:"foreignKey:VersionID"`
}

// Comment 评论模型
type Comment struct {
	ID        string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	TaskID    string         `json:"task_id" gorm:"not null;type:varchar(36)"`
	Content   string         `json:"content" gorm:"not null;type:text"`
	Author    string         `json:"author" gorm:"not null;size:100"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate GORM钩子，创建前生成UUID
func (c *Comment) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

// BeforeCreate GORM钩子，创建前生成UUID
func (t *Task) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = uuid.New().String()
	}
	return nil
}

// TaskCreateRequest 创建任务请求结构
type TaskCreateRequest struct {
	Title          string       `json:"title" binding:"required,min=1,max=255"`
	Description    string       `json:"description"`
	Priority       TaskPriority `json:"priority"`
	Type           TaskType     `json:"type"`
	VersionID      *string      `json:"version_id"`
	Tags           []string     `json:"tags"`
	DueDate        *CustomDate  `json:"due_date"`
	StartDate      *CustomDate  `json:"start_date"`
	EndDate        *CustomDate  `json:"end_date"`
	EstimatedHours int          `json:"estimated_hours"`
	ParentTaskID   *string      `json:"parent_task_id"`
	AssignedTo     *string      `json:"assigned_to"`
	CreatedBy      *string      `json:"created_by"`
	TempTaskID     string       `json:"temp_task_id"` // 临时任务ID，用于关联富文本编辑器中的附件
}

// TaskUpdateRequest 更新任务请求结构
type TaskUpdateRequest struct {
	Title          string       `json:"title"`
	Description    string       `json:"description"`
	Priority       TaskPriority `json:"priority"`
	Type           TaskType     `json:"type"`
	VersionID      *string      `json:"version_id"`
	Tags           []string     `json:"tags"`
	DueDate        *CustomDate  `json:"due_date"`
	StartDate      *CustomDate  `json:"start_date"`
	EndDate        *CustomDate  `json:"end_date"`
	EstimatedHours int          `json:"estimated_hours"`
	ActualHours    int          `json:"actual_hours"`
	AssignedTo     *string      `json:"assigned_to"`
}

// TaskStatusUpdateRequest 更新任务状态请求结构
type TaskStatusUpdateRequest struct {
	Status TaskStatus `json:"status" binding:"required"`
}

// TaskPositionUpdateRequest 更新任务位置请求结构
type TaskPositionUpdateRequest struct {
	Status   TaskStatus `json:"status" binding:"required"`
	Position int        `json:"position" binding:"min=0"`
}

// GetStatusDisplayName 获取状态显示名称
func (s TaskStatus) GetDisplayName() string {
	switch s {
	case StatusTodo:
		return "待办"
	case StatusInProgress:
		return "进行中"
	case StatusDone:
		return "已完成"
	default:
		return "未知"
	}
}

// GetPriorityDisplayName 获取优先级显示名称
func (p TaskPriority) GetDisplayName() string {
	switch p {
	case PriorityLow:
		return "低"
	case PriorityMedium:
		return "中"
	case PriorityHigh:
		return "高"
	default:
		return "中"
	}
}

// GetTypeDisplayName 获取任务类型显示名称
func (t TaskType) GetDisplayName() string {
	switch t {
	case TypeFeature:
		return "功能开发"
	case TypeBug:
		return "缺陷修复"
	case TypeTask:
		return "一般任务"
	case TypeImprovement:
		return "改进优化"
	case TypeResearch:
		return "研究调研"
	case TypeTesting:
		return "测试"
	case TypeDocumentation:
		return "文档"
	case TypeMaintenance:
		return "维护"
	default:
		return "一般任务"
	}
}

// GetTypeIcon 获取任务类型图标
func (t TaskType) GetTypeIcon() string {
	switch t {
	case TypeFeature:
		return "🚀"
	case TypeBug:
		return "🐛"
	case TypeTask:
		return "📋"
	case TypeImprovement:
		return "⚡"
	case TypeResearch:
		return "🔍"
	case TypeTesting:
		return "🧪"
	case TypeDocumentation:
		return "📚"
	case TypeMaintenance:
		return "🔧"
	default:
		return "📋"
	}
}

// GetTags 获取任务标签列表
func (t *Task) GetTags() []string {
	if t.Tags == "" {
		return []string{}
	}

	var tags []string
	if err := json.Unmarshal([]byte(t.Tags), &tags); err != nil {
		return []string{}
	}
	return tags
}

// SetTags 设置任务标签
func (t *Task) SetTags(tags []string) error {
	if tags == nil {
		tags = []string{}
	}

	tagsJSON, err := json.Marshal(tags)
	if err != nil {
		return err
	}

	t.Tags = string(tagsJSON)
	return nil
}

// IsOverdue 检查任务是否过期
func (t *Task) IsOverdue() bool {
	if t.DueDate == nil {
		return false
	}
	return time.Now().After(*t.DueDate) && t.Status != StatusDone
}

// GetProgress 获取任务进度百分比
func (t *Task) GetProgress() int {
	if t.EstimatedHours == 0 {
		return 0
	}

	progress := (t.ActualHours * 100) / t.EstimatedHours
	if progress > 100 {
		return 100
	}
	return progress
}

// GetPriorityColor 获取优先级颜色类
func (p TaskPriority) GetPriorityColor() string {
	switch p {
	case PriorityLow:
		return "text-green-600"
	case PriorityMedium:
		return "text-yellow-600"
	case PriorityHigh:
		return "text-red-600"
	default:
		return "text-gray-600"
	}
}

// TaskStatistics 任务统计信息
type TaskStatistics struct {
	TotalTasks        int                  `json:"total_tasks"`
	CompletedTasks    int                  `json:"completed_tasks"`
	InProgressTasks   int                  `json:"in_progress_tasks"`
	TodoTasks         int                  `json:"todo_tasks"`
	CompletionRate    float64              `json:"completion_rate"`
	TasksByPriority   map[TaskPriority]int `json:"tasks_by_priority"`
	TasksByStatus     map[TaskStatus]int   `json:"tasks_by_status"`
	RecentActivity    []TaskActivity       `json:"recent_activity"`
	ProductivityTrend []ProductivityData   `json:"productivity_trend"`
}

// TaskActivity 任务活动记录
type TaskActivity struct {
	TaskID      string    `json:"task_id"`
	TaskTitle   string    `json:"task_title"`
	Action      string    `json:"action"`
	Timestamp   time.Time `json:"timestamp"`
	Description string    `json:"description"`
}

// ProductivityData 生产力数据
type ProductivityData struct {
	Date           string  `json:"date"`
	TasksCreated   int     `json:"tasks_created"`
	TasksCompleted int     `json:"tasks_completed"`
	Productivity   float64 `json:"productivity"`
}

// GetDuration 获取任务持续时间（天数）
func (t *Task) GetDuration() int {
	if t.StartDate == nil || t.EndDate == nil {
		return 0
	}
	duration := t.EndDate.Sub(*t.StartDate)
	return int(duration.Hours() / 24)
}

// GetTimeStatus 获取任务时间状态
func (t *Task) GetTimeStatus() string {
	now := time.Now()

	if t.Status == StatusDone {
		return "completed"
	}

	if t.DueDate != nil && now.After(*t.DueDate) {
		return "overdue"
	}

	if t.DueDate != nil {
		daysUntilDue := int(t.DueDate.Sub(now).Hours() / 24)
		if daysUntilDue <= 1 {
			return "due-soon"
		} else if daysUntilDue <= 3 {
			return "due-this-week"
		}
	}

	return "normal"
}

// GetTimeStatusColor 获取时间状态对应的颜色类
func (t *Task) GetTimeStatusColor() string {
	switch t.GetTimeStatus() {
	case "overdue":
		return "text-red-600 bg-red-100"
	case "due-soon":
		return "text-orange-600 bg-orange-100"
	case "due-this-week":
		return "text-yellow-600 bg-yellow-100"
	case "completed":
		return "text-green-600 bg-green-100"
	default:
		return "text-gray-600 bg-gray-100"
	}
}

// FormatDateRange 格式化日期范围显示
func (t *Task) FormatDateRange() string {
	if t.StartDate == nil && t.EndDate == nil {
		return ""
	}

	if t.StartDate != nil && t.EndDate != nil {
		if t.StartDate.Format("2006-01-02") == t.EndDate.Format("2006-01-02") {
			return t.StartDate.Format("2006-01-02")
		}
		return t.StartDate.Format("01-02") + " ~ " + t.EndDate.Format("01-02")
	}

	if t.StartDate != nil {
		return "从 " + t.StartDate.Format("01-02")
	}

	if t.EndDate != nil {
		return "至 " + t.EndDate.Format("01-02")
	}

	return ""
}

// TimePreset 时间预设结构
type TimePreset struct {
	Name      string `json:"name"`
	Days      int    `json:"days"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

// GetTimePresets 获取时间预设选项
func GetTimePresets() []TimePreset {
	now := time.Now()

	return []TimePreset{
		{
			Name:      "今天",
			Days:      1,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.Format("2006-01-02"),
		},
		{
			Name:      "1天",
			Days:      1,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 1).Format("2006-01-02"),
		},
		{
			Name:      "3天",
			Days:      3,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 3).Format("2006-01-02"),
		},
		{
			Name:      "1周",
			Days:      7,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 7).Format("2006-01-02"),
		},
		{
			Name:      "2周",
			Days:      14,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 14).Format("2006-01-02"),
		},
		{
			Name:      "1个月",
			Days:      30,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 1, 0).Format("2006-01-02"),
		},
		{
			Name:      "本周",
			Days:      7,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 6).Format("2006-01-02"),
		},
		{
			Name:      "下周",
			Days:      7,
			StartDate: now.AddDate(0, 0, 7).Format("2006-01-02"),
			EndDate:   now.AddDate(0, 0, 13).Format("2006-01-02"),
		},
		{
			Name:      "本月",
			Days:      30,
			StartDate: now.Format("2006-01-02"),
			EndDate:   now.AddDate(0, 1, 0).Format("2006-01-02"),
		},
	}
}
