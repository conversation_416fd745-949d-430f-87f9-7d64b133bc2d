// 登录页面交互增强
// Login Page Interactive Enhancements

document.addEventListener('DOMContentLoaded', function() {
    initializeLoginPage();
});

// 初始化登录页面
function initializeLoginPage() {
    setupFormValidation();
    setupKeyboardShortcuts();
    setupAnimations();
    setupThemeToggle();
}

// 设置表单验证
function setupFormValidation() {
    const form = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    // 实时验证
    usernameInput.addEventListener('input', function() {
        validateUsername(this);
    });
    
    passwordInput.addEventListener('input', function() {
        validatePassword(this);
    });
    
    // 表单提交验证
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
    });
}

// 验证用户名
function validateUsername(input) {
    const value = input.value.trim();
    const isValid = value.length >= 3;
    
    updateInputState(input, isValid);
    return isValid;
}

// 验证密码
function validatePassword(input) {
    const value = input.value;
    const isValid = value.length >= 6;
    
    updateInputState(input, isValid);
    return isValid;
}

// 更新输入框状态
function updateInputState(input, isValid) {
    if (input.value.length === 0) {
        // 空值时移除所有状态
        input.classList.remove('border-green-500', 'border-red-500');
        return;
    }
    
    if (isValid) {
        input.classList.remove('border-red-500');
        input.classList.add('border-green-500');
    } else {
        input.classList.remove('border-green-500');
        input.classList.add('border-red-500');
    }
}

// 验证整个表单
function validateForm() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    const isUsernameValid = validateUsername(usernameInput);
    const isPasswordValid = validatePassword(passwordInput);
    
    return isUsernameValid && isPasswordValid;
}

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + Enter 快速登录
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }
        
        // ESC 清空表单
        if (e.key === 'Escape') {
            clearForm();
        }
    });
}

// 清空表单
function clearForm() {
    const form = document.getElementById('loginForm');
    const inputs = form.querySelectorAll('input');
    
    inputs.forEach(input => {
        input.value = '';
        input.classList.remove('border-green-500', 'border-red-500');
    });
    
    hideMessage();
}

// 设置动画效果
function setupAnimations() {
    // 输入框焦点动画和微交互
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focused');
            // 添加轻微的缩放效果
            this.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focused');
            this.style.transform = 'scale(1)';
        });

        // 输入时的动画反馈
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.style.borderColor = '#10b981';
                this.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
            } else {
                this.style.borderColor = '#e1e5e9';
                this.style.boxShadow = 'none';
            }
        });
    });

    // 按钮悬停效果增强
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
        loginBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        loginBtn.addEventListener('mouseleave', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    }

    // 添加页面元素的渐入动画
    const elements = document.querySelectorAll('.form-group, .login-btn, .default-accounts');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';

        setTimeout(() => {
            element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 200 + index * 100);
    });
}

// 设置主题切换
function setupThemeToggle() {
    // 检测系统主题偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const savedTheme = localStorage.getItem('theme');
    
    if (savedTheme) {
        setTheme(savedTheme);
    } else if (prefersDark) {
        setTheme('dark');
    }
    
    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            setTheme(e.matches ? 'dark' : 'light');
        }
    });
}

// 设置主题
function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
}

// 显示消息（增强版）
function showMessage(message, type, duration = 3000) {
    const container = document.getElementById('message-container');
    const messageEl = document.getElementById('message');
    
    // 设置消息内容和样式
    messageEl.textContent = message;
    messageEl.className = `p-4 rounded-xl text-sm font-medium ${
        type === 'error' ? 'error-message' : 'success-message'
    }`;
    
    // 显示消息
    container.classList.remove('hidden');
    
    // 添加进入动画
    messageEl.style.transform = 'translateY(-10px)';
    messageEl.style.opacity = '0';
    
    setTimeout(() => {
        messageEl.style.transition = 'all 0.3s ease-out';
        messageEl.style.transform = 'translateY(0)';
        messageEl.style.opacity = '1';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        hideMessage();
    }, duration);
}

// 隐藏消息
function hideMessage() {
    const container = document.getElementById('message-container');
    const messageEl = document.getElementById('message');
    
    messageEl.style.transition = 'all 0.3s ease-in';
    messageEl.style.transform = 'translateY(-10px)';
    messageEl.style.opacity = '0';
    
    setTimeout(() => {
        container.classList.add('hidden');
    }, 300);
}

// 增强的登录处理
async function handleLogin(username, password) {
    const loginBtn = document.getElementById('loginBtn');
    const loginText = document.getElementById('loginText');
    const loginSpinner = document.getElementById('loginSpinner');
    
    try {
        // 显示加载状态
        setLoginButtonState(true, '登录中...', true);
        
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('登录成功，正在跳转...', 'success');
            
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } else {
            showMessage(data.message || '登录失败', 'error');
            setLoginButtonState(false, '登录', false);
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('网络错误，请稍后重试', 'error');
        setLoginButtonState(false, '登录', false);
    }
}

// 设置登录按钮状态
function setLoginButtonState(disabled, text, showSpinner) {
    const loginBtn = document.getElementById('loginBtn');
    const loginText = document.getElementById('loginText');
    const loginSpinner = document.getElementById('loginSpinner');
    
    loginBtn.disabled = disabled;
    loginText.textContent = text;
    
    if (showSpinner) {
        loginSpinner.classList.remove('hidden');
    } else {
        loginSpinner.classList.add('hidden');
    }
}

// 导出函数供全局使用
window.LoginPage = {
    showMessage,
    hideMessage,
    handleLogin,
    validateForm,
    clearForm,
    setTheme
};
