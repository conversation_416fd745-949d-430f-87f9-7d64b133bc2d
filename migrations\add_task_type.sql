-- 添加任务类型字段
ALTER TABLE tasks ADD COLUMN type VARCHAR(50) NOT NULL DEFAULT 'task';

-- 更新现有任务的类型（可以根据标题或描述关键词智能分类）
UPDATE tasks SET type = 'bug' WHERE title LIKE '%bug%' OR title LIKE '%缺陷%' OR title LIKE '%修复%';
UPDATE tasks SET type = 'feature' WHERE title LIKE '%功能%' OR title LIKE '%新增%' OR title LIKE '%开发%';
UPDATE tasks SET type = 'testing' WHERE title LIKE '%测试%' OR title LIKE '%test%';
UPDATE tasks SET type = 'documentation' WHERE title LIKE '%文档%' OR title LIKE '%doc%';
UPDATE tasks SET type = 'improvement' WHERE title LIKE '%优化%' OR title LIKE '%改进%' OR title LIKE '%提升%';
UPDATE tasks SET type = 'research' WHERE title LIKE '%研究%' OR title LIKE '%调研%' OR title LIKE '%分析%';
UPDATE tasks SET type = 'maintenance' WHERE title LIKE '%维护%' OR title LIKE '%清理%';
