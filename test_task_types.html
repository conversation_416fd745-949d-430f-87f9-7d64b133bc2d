<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务类型管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>🏷️ 任务类型管理功能测试</h1>
    
    <div class="test-section">
        <h2>API 测试</h2>
        <button class="test-button" onclick="testGetTaskTypes()">获取任务类型</button>
        <button class="test-button" onclick="testCreateTaskType()">创建测试类型</button>
        <button class="test-button" onclick="testDeleteTestType()">删除测试类型</button>
        <div id="apiResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>前端功能测试</h2>
        <button class="test-button" onclick="openTaskTypeManager()">打开类型管理器</button>
        <div id="frontendResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>任务类型列表</h2>
        <div id="taskTypesList"></div>
    </div>

    <script>
        let testTypeId = null;

        // 测试获取任务类型
        async function testGetTaskTypes() {
            try {
                const response = await fetch('/api/task-types');
                const data = await response.json();
                
                if (data.task_types) {
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="success">✅ 成功获取 ${data.task_types.length} 个任务类型</div>`;
                    
                    // 显示类型列表
                    const listHtml = data.task_types.map(type => 
                        `<div style="margin: 5px 0; padding: 5px; border: 1px solid #eee;">
                            <strong>${type.icon} ${type.display_name}</strong> (${type.name})
                            <br><small>颜色: ${type.color} | 系统类型: ${type.is_system ? '是' : '否'}</small>
                        </div>`
                    ).join('');
                    document.getElementById('taskTypesList').innerHTML = listHtml;
                } else {
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="error">❌ 获取失败: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                document.getElementById('apiResult').innerHTML = 
                    `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 测试创建任务类型
        async function testCreateTaskType() {
            const testType = {
                name: 'test_type_' + Date.now(),
                display_name: '测试类型',
                icon: '🧪',
                color: '#FF6B6B',
                description: '这是一个测试类型',
                sort_order: 999
            };

            try {
                const response = await fetch('/api/task-types', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testType)
                });
                
                const data = await response.json();
                
                if (data.task_type) {
                    testTypeId = data.task_type.id;
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="success">✅ 成功创建测试类型: ${data.task_type.display_name}</div>`;
                    testGetTaskTypes(); // 刷新列表
                } else {
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="error">❌ 创建失败: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                document.getElementById('apiResult').innerHTML = 
                    `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 测试删除测试类型
        async function testDeleteTestType() {
            if (!testTypeId) {
                document.getElementById('apiResult').innerHTML = 
                    `<div class="error">❌ 请先创建测试类型</div>`;
                return;
            }

            try {
                const response = await fetch(`/api/task-types/${testTypeId}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="success">✅ 成功删除测试类型</div>`;
                    testTypeId = null;
                    testGetTaskTypes(); // 刷新列表
                } else {
                    document.getElementById('apiResult').innerHTML = 
                        `<div class="error">❌ 删除失败: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                document.getElementById('apiResult').innerHTML = 
                    `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 模拟打开类型管理器（需要在实际页面中测试）
        function openTaskTypeManager() {
            document.getElementById('frontendResult').innerHTML = 
                `<div class="success">✅ 请在主页面 (http://localhost:8090) 中点击"🏷️ 类型管理"按钮测试前端功能</div>`;
        }

        // 页面加载时自动测试
        window.onload = function() {
            testGetTaskTypes();
        };
    </script>
</body>
</html>
