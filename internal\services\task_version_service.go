package services

import (
	"errors"
	"projectm2/internal/models"

	"gorm.io/gorm"
)

// TaskVersionService 任务版本服务
type TaskVersionService struct {
	db *gorm.DB
}

// NewTaskVersionService 创建新的任务版本服务实例
func NewTaskVersionService(db *gorm.DB) *TaskVersionService {
	return &TaskVersionService{db: db}
}

// GetAllTaskVersions 获取所有任务版本
func (s *TaskVersionService) GetAllTaskVersions() ([]models.TaskVersionResponse, error) {
	var versions []models.TaskVersion
	err := s.db.Where("deleted_at IS NULL").Order("sort_order ASC, created_at ASC").Find(&versions).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式并获取任务数量
	var responses []models.TaskVersionResponse
	for _, version := range versions {
		response := version.ToResponse()
		
		// 获取该版本的任务数量
		var taskCount int64
		s.db.Model(&models.Task{}).Where("version_id = ? AND deleted_at IS NULL", version.ID).Count(&taskCount)
		response.TaskCount = taskCount
		
		responses = append(responses, response)
	}

	return responses, nil
}

// GetTaskVersionByID 根据ID获取任务版本
func (s *TaskVersionService) GetTaskVersionByID(id string) (*models.TaskVersionResponse, error) {
	var version models.TaskVersion
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&version).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("版本不存在")
		}
		return nil, err
	}

	response := version.ToResponse()
	
	// 获取该版本的任务数量
	var taskCount int64
	s.db.Model(&models.Task{}).Where("version_id = ? AND deleted_at IS NULL", version.ID).Count(&taskCount)
	response.TaskCount = taskCount

	return &response, nil
}

// CreateTaskVersion 创建任务版本
func (s *TaskVersionService) CreateTaskVersion(req models.TaskVersionCreateRequest) (*models.TaskVersionResponse, error) {
	// 检查名称是否已存在
	var existingVersion models.TaskVersion
	err := s.db.Where("name = ? AND deleted_at IS NULL", req.Name).First(&existingVersion).Error
	if err == nil {
		return nil, errors.New("版本名称已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 创建新版本
	version := models.TaskVersion{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Color:       req.Color,
		Icon:        req.Icon,
		IsActive:    true,
		SortOrder:   req.SortOrder,
	}

	err = s.db.Create(&version).Error
	if err != nil {
		return nil, err
	}

	response := version.ToResponse()
	response.TaskCount = 0 // 新创建的版本任务数量为0

	return &response, nil
}

// UpdateTaskVersion 更新任务版本
func (s *TaskVersionService) UpdateTaskVersion(id string, req models.TaskVersionUpdateRequest) (*models.TaskVersionResponse, error) {
	var version models.TaskVersion
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&version).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("版本不存在")
		}
		return nil, err
	}

	// 检查名称是否与其他版本冲突
	if req.Name != version.Name {
		var existingVersion models.TaskVersion
		err := s.db.Where("name = ? AND id != ? AND deleted_at IS NULL", req.Name, id).First(&existingVersion).Error
		if err == nil {
			return nil, errors.New("版本名称已存在")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	// 更新版本信息
	version.Name = req.Name
	version.DisplayName = req.DisplayName
	version.Description = req.Description
	version.Color = req.Color
	version.Icon = req.Icon
	version.SortOrder = req.SortOrder
	
	if req.IsActive != nil {
		version.IsActive = *req.IsActive
	}

	err = s.db.Save(&version).Error
	if err != nil {
		return nil, err
	}

	response := version.ToResponse()
	
	// 获取该版本的任务数量
	var taskCount int64
	s.db.Model(&models.Task{}).Where("version_id = ? AND deleted_at IS NULL", version.ID).Count(&taskCount)
	response.TaskCount = taskCount

	return &response, nil
}

// DeleteTaskVersion 删除任务版本
func (s *TaskVersionService) DeleteTaskVersion(id string) error {
	var version models.TaskVersion
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&version).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("版本不存在")
		}
		return err
	}

	// 检查是否有任务使用该版本
	var taskCount int64
	s.db.Model(&models.Task{}).Where("version_id = ? AND deleted_at IS NULL", id).Count(&taskCount)
	if taskCount > 0 {
		return errors.New("该版本下还有任务，无法删除")
	}

	// 软删除版本
	err = s.db.Delete(&version).Error
	if err != nil {
		return err
	}

	return nil
}

// GetActiveTaskVersions 获取所有激活的任务版本（用于下拉选择）
func (s *TaskVersionService) GetActiveTaskVersions() ([]models.TaskVersionResponse, error) {
	var versions []models.TaskVersion
	err := s.db.Where("is_active = ? AND deleted_at IS NULL", true).Order("sort_order ASC, created_at ASC").Find(&versions).Error
	if err != nil {
		return nil, err
	}

	var responses []models.TaskVersionResponse
	for _, version := range versions {
		responses = append(responses, version.ToResponse())
	}

	return responses, nil
}

// GetTaskVersionStats 获取版本统计信息
func (s *TaskVersionService) GetTaskVersionStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总版本数
	var totalVersions int64
	s.db.Model(&models.TaskVersion{}).Where("deleted_at IS NULL").Count(&totalVersions)
	stats["total_versions"] = totalVersions

	// 激活版本数
	var activeVersions int64
	s.db.Model(&models.TaskVersion{}).Where("is_active = ? AND deleted_at IS NULL", true).Count(&activeVersions)
	stats["active_versions"] = activeVersions

	// 每个版本的任务数量
	var versionTaskCounts []map[string]interface{}
	rows, err := s.db.Raw(`
		SELECT tv.id, tv.name, tv.display_name, tv.color, tv.icon, COUNT(t.id) as task_count
		FROM task_versions tv
		LEFT JOIN tasks t ON tv.id = t.version_id AND t.deleted_at IS NULL
		WHERE tv.deleted_at IS NULL
		GROUP BY tv.id, tv.name, tv.display_name, tv.color, tv.icon
		ORDER BY tv.sort_order ASC, tv.created_at ASC
	`).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var id, name, displayName, color, icon string
		var taskCount int64
		rows.Scan(&id, &name, &displayName, &color, &icon, &taskCount)
		versionTaskCounts = append(versionTaskCounts, map[string]interface{}{
			"id":           id,
			"name":         name,
			"display_name": displayName,
			"color":        color,
			"icon":         icon,
			"task_count":   taskCount,
		})
	}
	stats["version_task_counts"] = versionTaskCounts

	return stats, nil
}
