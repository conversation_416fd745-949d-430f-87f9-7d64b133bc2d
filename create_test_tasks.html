<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试任务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #results {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 创建测试任务</h1>
        <p>用于测试"查看更多历史任务"功能</p>
        
        <div>
            <button class="button" onclick="createTestTasks('todo', 8)">创建8个待办任务</button>
            <button class="button" onclick="createTestTasks('in_progress', 7)">创建7个进行中任务</button>
            <button class="button" onclick="createTestTasks('done', 6)">创建6个已完成任务</button>
            <button class="button success" onclick="createAllTestTasks()">一键创建所有测试任务</button>
            <button class="button danger" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const baseURL = 'http://localhost:8093';
        
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function createTestTasks(status, count) {
            showResult(`🚀 开始创建 ${count} 个 ${status} 状态的测试任务...`, 'info');
            showResult(`💡 提示：请确保您已在主页面登录`, 'info');

            const statusNames = {
                'todo': '待办',
                'in_progress': '进行中',
                'done': '已完成'
            };

            let successCount = 0;
            let errorCount = 0;

            for (let i = 1; i <= count; i++) {
                try {
                    // 创建任务
                    const taskData = {
                        title: `测试任务 ${statusNames[status]} ${i}`,
                        description: `这是第 ${i} 个${statusNames[status]}测试任务，用于验证查看更多历史任务功能。`,
                        priority: ['low', 'medium', 'high'][i % 3],
                        type: 'task'
                    };

                    const createResponse = await fetch(`${baseURL}/api/tasks`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include', // 包含cookies
                        body: JSON.stringify(taskData)
                    });

                    if (!createResponse.ok) {
                        if (createResponse.status === 401) {
                            throw new Error(`认证失败，请先在主页面登录`);
                        }
                        throw new Error(`创建任务失败: ${createResponse.status}`);
                    }

                    const createResult = await createResponse.json();
                    const taskId = createResult.task.id;

                    // 如果不是待办状态，需要更新状态
                    if (status !== 'todo') {
                        const statusData = { status: status };
                        const updateResponse = await fetch(`${baseURL}/api/tasks/${taskId}/status`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            credentials: 'include', // 包含cookies
                            body: JSON.stringify(statusData)
                        });

                        if (!updateResponse.ok) {
                            throw new Error(`更新任务状态失败: ${updateResponse.status}`);
                        }
                    }

                    successCount++;
                    showResult(`✅ 成功创建任务 ${i}: ${taskData.title}`, 'success');

                    // 添加小延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 100));

                } catch (error) {
                    errorCount++;
                    showResult(`❌ 创建任务 ${i} 失败: ${error.message}`, 'error');

                    // 如果是认证错误，停止创建
                    if (error.message.includes('认证失败')) {
                        showResult(`🛑 停止创建，请先登录主页面`, 'error');
                        break;
                    }
                }
            }

            showResult(`🎯 ${statusNames[status]}任务创建完成: 成功 ${successCount}，失败 ${errorCount}`, successCount > 0 ? 'success' : 'error');
        }
        
        async function createAllTestTasks() {
            showResult('🚀 开始创建所有测试任务...', 'info');
            
            try {
                await createTestTasks('todo', 8);
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await createTestTasks('in_progress', 7);
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await createTestTasks('done', 6);
                
                showResult('🎉 所有测试任务创建完成！现在可以测试查看更多历史任务功能了。', 'success');
                showResult('💡 请刷新主页面查看效果', 'info');
                
            } catch (error) {
                showResult(`❌ 批量创建失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            showResult('📋 测试说明：', 'info');
            showResult('1. 点击按钮创建测试任务', 'info');
            showResult('2. 每个状态需要超过5个任务才会显示"查看更多"按钮', 'info');
            showResult('3. 创建完成后刷新主页面查看效果', 'info');
        };
    </script>
</body>
</html>
