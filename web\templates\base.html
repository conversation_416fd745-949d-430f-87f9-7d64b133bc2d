<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="现代化的项目管理看板系统，支持拖拽、实时同步、离线工作">
    <meta name="keywords" content="项目管理,看板,任务管理,拖拽,Kanban">
    <meta name="author" content="ProjectM2">
    <title>{{.title}}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📋</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📋</text></svg>">
    
    <!-- TailwindCSS CDN (开发环境) -->
    <script>
        // 抑制生产环境警告 - 在加载 TailwindCSS 之前设置
        if (typeof console !== 'undefined' && console.warn) {
            const originalWarn = console.warn;
            console.warn = function(...args) {
                const message = args[0];
                if (message && typeof message === 'string' &&
                    (message.includes('cdn.tailwindcss.com should not be used in production') ||
                     message.includes('tailwindcss.com should not be used in production'))) {
                    return; // 忽略 TailwindCSS 生产环境警告
                }
                originalWarn.apply(console, args);
            };
        }
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 生产环境时请替换为本地构建版本 -->
    <script>
        if (typeof tailwind !== 'undefined') {
            tailwind.config = {
                corePlugins: {
                    preflight: false,
                }
            }
        }
    </script>

    <!-- SortableJS CDN with fallback -->
    <script>
        // 智能 CDN 加载器 - 多源回退机制
        (function() {
            const sortableCDNs = [
                'https://unpkg.com/sortablejs@1.15.0/Sortable.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js',
                'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
                'https://cdn.bootcdn.net/ajax/libs/Sortable/1.15.0/Sortable.min.js'
            ];

            let currentCDNIndex = 0;

            function loadSortableJS() {
                if (currentCDNIndex >= sortableCDNs.length) {
                    console.warn('⚠️ 所有 SortableJS CDN 源都失败了，使用本地备用方案');
                    loadLocalFallback();
                    return;
                }

                const script = document.createElement('script');
                script.src = sortableCDNs[currentCDNIndex];
                script.async = false;

                script.onload = function() {
                    console.log('✅ SortableJS 加载成功，来源:', sortableCDNs[currentCDNIndex]);
                    // 触发自定义事件通知 SortableJS 已加载
                    window.dispatchEvent(new CustomEvent('sortableLoaded'));
                };

                script.onerror = function() {
                    console.warn('⚠️ CDN 源失败:', sortableCDNs[currentCDNIndex]);
                    currentCDNIndex++;
                    loadSortableJS(); // 尝试下一个 CDN 源
                };

                document.head.appendChild(script);
            }

            function loadLocalFallback() {
                const script = document.createElement('script');
                script.src = '/static/libs/sortable-fallback.js';
                script.async = false;

                script.onload = function() {
                    console.log('✅ SortableJS 本地备用方案加载成功');
                    window.dispatchEvent(new CustomEvent('sortableLoaded'));
                };

                script.onerror = function() {
                    console.error('❌ SortableJS 本地备用方案也加载失败');
                    if (typeof showToast === 'function') {
                        showToast('拖拽功能暂时不可用，请刷新页面重试', 'error');
                    }
                };

                document.head.appendChild(script);
            }

            // 开始加载
            loadSortableJS();
        })();
    </script>

    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Quill.js Rich Text Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/static/css/style.css?v=18">

    <!-- 任务类型管理专用样式 -->
    <style>
        /* 简洁的模态框动画 */
        #taskTypeModal {
            transition: opacity 0.2s ease;
        }

        /* 模板管理专用样式 */
        .template-card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .template-card-hover:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .template-button-hover {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .template-button-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .template-button-hover:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 模板卡片样式增强 */
        .template-card {
            position: relative;
            overflow: hidden;
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .template-card:hover::before {
            transform: scaleX(1);
        }

        /* 文本截断样式 */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 按钮悬停效果增强 */
        .template-card button {
            position: relative;
            overflow: hidden;
        }

        .template-card button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .template-card button:hover::before {
            width: 100px;
            height: 100px;
        }

        /* 快速模板选择器样式 */
        #quickTemplateDropdown {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        #quickTemplateDropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid white;
            filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
        }

        /* 快速模板按钮悬停效果 */
        #quickTemplateBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
        }

        /* 加载动画 */
        @keyframes pulse-soft {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .animate-pulse-soft {
            animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 渐入动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.5s ease-out;
        }

        /* 任务详情模态框专业样式 */
        #taskDetailModal {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        #taskDetailModal .modal-content {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(229, 231, 235, 0.8);
        }

        /* 专业状态标签样式 */
        .status-badge-todo {
            background-color: #fef3c7;
            color: #92400e;
            border-color: #fbbf24;
        }

        .status-badge-in-progress {
            background-color: #dbeafe;
            color: #1e40af;
            border-color: #3b82f6;
        }

        .status-badge-done {
            background-color: #d1fae5;
            color: #065f46;
            border-color: #10b981;
        }

        /* 专业优先级标签样式 */
        .priority-badge-low {
            background-color: #f0fdf4;
            color: #166534;
            border-color: #22c55e;
        }

        .priority-badge-medium {
            background-color: #fffbeb;
            color: #92400e;
            border-color: #f59e0b;
        }

        .priority-badge-high {
            background-color: #fef2f2;
            color: #991b1b;
            border-color: #ef4444;
        }

        /* 商务级卡片样式 */
        .business-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .business-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-color: #d1d5db;
        }

        /* 专业按钮样式 */
        .btn-business-primary {
            background-color: #2563eb;
            border-color: #2563eb;
            color: #ffffff;
            font-weight: 500;
            letter-spacing: 0.025em;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-business-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .btn-business-secondary {
            background-color: #ffffff;
            border-color: #d1d5db;
            color: #374151;
            font-weight: 500;
            letter-spacing: 0.025em;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-business-secondary:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-business-danger {
            background-color: #ffffff;
            border-color: #dc2626;
            color: #dc2626;
            font-weight: 500;
            letter-spacing: 0.025em;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-business-danger:hover {
            background-color: #fef2f2;
            border-color: #b91c1c;
            color: #b91c1c;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
        }

        /* 专业进度条样式 */
        .progress-bar-business {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .progress-bar-business::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 专业数据展示样式 */
        .data-metric {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-weight: 600;
            letter-spacing: -0.025em;
        }

        /* 专业表格样式 */
        .business-table {
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .business-table th {
            background-color: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-size: 0.75rem;
            color: #374151;
        }

        .business-table td {
            border-bottom: 1px solid #f3f4f6;
        }

        .business-table tr:last-child td {
            border-bottom: none;
        }

        /* 移动端紧凑优化 */
        @media (max-width: 768px) {
            #taskDetailModal {
                padding: 0.25rem;
            }

            #taskDetailModal > div {
                margin: 0;
                max-height: calc(100vh - 0.5rem);
                border-radius: 8px;
            }

            /* 移动端头部优化 */
            #taskDetailModal .bg-gradient-to-r {
                padding: 0.75rem;
            }

            #taskDetailModal h3 {
                font-size: 1rem;
            }

            /* 移动端网格布局调整 */
            .grid.lg\\:grid-cols-3 {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .grid.md\\:grid-cols-3 {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .grid.md\\:grid-cols-2 {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            /* 移动端操作栏 */
            #taskDetailModal .bg-gray-50:last-child {
                padding: 0.75rem;
            }

            #taskDetailModal .bg-gray-50:last-child > div {
                flex-direction: column;
                gap: 0.5rem;
            }

            #taskDetailModal .bg-gray-50:last-child .flex.space-x-2 {
                flex-direction: column;
                gap: 0.5rem;
            }

            /* 移动端文字大小调整 */
            #taskDetailModal h2 {
                font-size: 1.25rem;
                line-height: 1.4;
            }

            #taskDetailModal .text-2xl {
                font-size: 1.5rem;
            }

            /* 移动端间距优化 */
            #taskDetailModal .p-4 {
                padding: 0.75rem;
            }

            #taskDetailModal .p-3 {
                padding: 0.5rem;
            }

            /* 移动端描述区域 */
            #detailDescription {
                font-size: 0.75rem;
                line-height: 1.4;
                padding: 0.5rem;
            }

            /* 移动端按钮优化 */
            #taskDetailModal button {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            #taskDetailModal {
                padding: 0.25rem;
            }

            #taskDetailModal > div {
                border-radius: 8px;
                max-height: calc(100vh - 0.5rem);
            }

            #taskDetailModal .bg-gradient-to-r {
                padding: 0.75rem;
            }

            #taskDetailModal h3 {
                font-size: 1rem;
            }

            #taskDetailModal h2 {
                font-size: 1.25rem;
            }

            .business-card {
                padding: 0.75rem;
            }

            #taskDetailModal .text-3xl {
                font-size: 1.5rem;
            }

            #taskDetailModal .p-6 {
                padding: 0.75rem;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn-business-primary,
            .btn-business-secondary,
            .btn-business-danger {
                min-height: 44px;
                touch-action: manipulation;
            }

            #taskDetailModal button {
                min-height: 44px;
                touch-action: manipulation;
            }

            /* 移除悬停效果 */
            .btn-business-primary:hover,
            .btn-business-secondary:hover,
            .btn-business-danger:hover {
                transform: none;
                box-shadow: none;
            }

            .business-card:hover {
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                border-color: #e5e7eb;
            }
        }
    </style>

    <!-- 自定义样式 -->
    <style>
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-chosen {
            transform: rotate(5deg);
        }
        .task-card {
            transition: all 0.2s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .column-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .priority-high {
            border-left: 4px solid #ef4444;
        }
        .priority-medium {
            border-left: 4px solid #f59e0b;
        }
        .priority-low {
            border-left: 4px solid #10b981;
        }

        /* 强制居中样式 */
        .force-center {
            width: 100% !important;
        }

        .center-content {
            width: 100% !important;
            margin: 0 !important;
        }

        /* 强制网格填充满容器 */
        .grid-responsive {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            gap: 0.5rem !important;
        }

        /* 确保任务栏列完全填充 */
        .grid-responsive > div {
            width: 100% !important;
        }

        /* 🎨 现代化导航按钮系统 */
        .nav-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .nav-btn:hover {
            background: white;
            border-color: #cbd5e1;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .nav-btn:active {
            transform: translateY(0);
        }

        .nav-btn-danger {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-color: #fecaca;
        }

        .nav-btn-danger:hover {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border-color: #fca5a5;
        }

        .nav-select {
            height: 36px;
            padding-left: 12px;
            padding-right: 32px;
            font-size: 14px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .nav-select:hover {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .nav-select:focus {
            outline: none;
            box-shadow: 0 0 0 2px #3b82f6;
            border-color: transparent;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-gray-900">{{.title}}</h1>
                            <p class="text-xs text-gray-500">现代化项目管理系统</p>
                        </div>
                    </div>
                </div>
                <!-- 桌面端导航 -->
                <div class="hidden-mobile flex items-center space-x-3">
                    <!-- 搜索框 -->
                    <div class="relative group">
                        <input type="text" id="searchInput" placeholder="搜索任务..."
                               class="w-64 h-10 pl-10 pr-4 text-sm bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:bg-white group-hover:shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-gray-400 group-hover:text-gray-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- 责任人按钮 -->
                    <button id="assigneeFilterBtn" onclick="showAssigneeSelector()"
                            class="h-10 px-4 text-sm bg-gray-50 border border-gray-200 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 flex items-center space-x-2 text-gray-600 hover:text-gray-800">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span id="assigneeFilterText">责任人</span>
                        <span id="assigneeFilterClear" class="hidden ml-1 text-gray-400 hover:text-red-500 cursor-pointer" onclick="clearAssigneeFilter(event)">
                            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </span>
                    </button>

                    <!-- 过滤器 -->
                    <select id="priorityFilter" class="nav-select">
                        <option value="">所有优先级</option>
                        <option value="high">🔴 高优先级</option>
                        <option value="medium">🟡 中优先级</option>
                        <option value="low">🟢 低优先级</option>
                    </select>



                    <button id="exportBtn" class="nav-btn group" title="导出数据" onclick="handleExportClick()">
                        <svg class="w-4 h-4 text-gray-500 group-hover:text-gray-700 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </button>





                    <a href="/users" id="userManagementBtn" class="nav-btn group hidden" title="用户管理">
                        <svg class="w-4 h-4 text-gray-500 group-hover:text-gray-700 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </a>

                    <!-- 当前用户信息 -->
                    <div class="relative">
                        <button id="currentUserInfo" class="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
                            <div class="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium" id="userAvatar">
                                U
                            </div>
                            <div class="text-xs">
                                <div class="font-medium text-gray-900" id="userDisplayName">加载中...</div>
                                <div class="text-gray-500 text-xs" id="userRole">-</div>
                            </div>
                            <svg class="w-3 h-3 text-gray-400 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- 用户下拉菜单 -->
                        <div id="userDropdown" class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                            <button id="personalSettingsBtn" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>个人设置</span>
                            </button>
                            <div class="border-t border-gray-100 my-1"></div>
                            <button id="logoutBtnDropdown" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                <span>退出登录</span>
                            </button>
                        </div>
                    </div>



                    <!-- 任务创建按钮组 -->
                    <div class="flex items-center space-x-2">
                        <!-- 新建任务按钮 -->
                        <button id="addTaskBtn" class="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>新建任务</span>
                        </button>

                        <!-- 快速模板选择器 - 所有用户可见 -->
                        <div class="relative" id="quickTemplateSelector">
                            <button id="quickTemplateBtn" class="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span class="hidden sm:inline">模板</span>
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 快速模板下拉菜单 -->
                            <div id="quickTemplateDropdown" class="absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 hidden transform opacity-0 scale-95 transition-all duration-200">
                                <div class="p-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                            <span class="text-purple-500 mr-2">📋</span>
                                            快速创建
                                        </h3>
                                        <button id="quickTemplateManageBtn" onclick="openTaskTemplateManager()" class="hidden text-sm text-purple-600 hover:text-purple-700 font-medium">
                                            管理模板 →
                                        </button>
                                    </div>

                                    <!-- 模板列表 -->
                                    <div id="quickTemplateList" class="space-y-2 max-h-64 overflow-y-auto custom-scrollbar">
                                        <!-- 模板项将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 移动端导航 -->
                <div class="mobile-only hidden flex items-center space-x-2">
                    <button id="mobileSearchToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    <button id="mobileAssigneeToggle" onclick="showAssigneeSelector()" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </button>
                    <button id="mobileFilterToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                        </svg>
                    </button>
                    <button id="mobileThemeToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                </div>

                </div>
            </div>
        </div>
    </nav>

    <!-- 移动端搜索面板 -->
    <div id="mobileSearchPanel" class="mobile-only hidden bg-white border-b border-gray-200 p-4">
        <div class="relative">
            <input type="text" id="mobileSearchInput" placeholder="搜索任务..."
                   class="form-input pl-10 pr-4 py-3 w-full">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 移动端过滤面板 -->
    <div id="mobileFilterPanel" class="mobile-only hidden bg-white border-b border-gray-200 p-4">
        <div class="space-y-3">
            <label class="block text-sm font-medium text-gray-700">优先级过滤</label>
            <select id="mobilePriorityFilter" class="form-input w-full">
                <option value="">所有优先级</option>
                <option value="high">🔴 高优先级</option>
                <option value="medium">🟡 中优先级</option>
                <option value="low">🟢 低优先级</option>
            </select>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="w-full px-2 py-6">
        {{template "content" .}}
    </main>

    <!-- 任务模态框 -->
    <div id="taskModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[95vh] overflow-y-auto relative z-[10000]">
                <form id="taskForm">
                    <div class="bg-white px-4 pt-4 pb-3 sm:px-6 sm:pt-5 sm:pb-4">
                        <div class="w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-3" id="modalTitle">
                                新建任务
                            </h3>
                            <div class="space-y-4">
                                <!-- 第一行：任务标题和版本选择 -->
                                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                                    <div class="lg:col-span-3">
                                        <label for="taskTitle" class="block text-sm font-medium text-gray-700 mb-1">
                                            任务标题 <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" id="taskTitle" name="title" required maxlength="255"
                                               class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                               placeholder="请输入任务标题（最多255字符）">
                                        <div class="mt-1 text-xs text-gray-500">
                                            <span id="titleCounter">0</span>/255 字符
                                        </div>
                                    </div>
                                    <div class="lg:col-span-1">
                                        <label for="taskVersion" class="block text-sm font-medium text-gray-700 mb-1">任务版本</label>
                                        <select id="taskVersion" name="version_id"
                                                class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">未指定版本</option>
                                            <!-- 版本选项将通过JavaScript动态加载 -->
                                        </select>
                                    </div>
                                </div>

                                <!-- 第二行：五列布局（优先级、任务类型、分配给、开始时间、结束时间） -->
                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                                    <div>
                                        <label for="taskPriority" class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                                        <select id="taskPriority" name="priority"
                                                class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="low">🟢 低优先级</option>
                                            <option value="medium" selected>🟡 中优先级</option>
                                            <option value="high">🔴 高优先级</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label for="taskType" class="block text-sm font-medium text-gray-700 mb-1">任务类型</label>
                                        <select id="taskType" name="type"
                                                class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <!-- 任务类型选项将通过JavaScript动态加载 -->
                                        </select>
                                    </div>

                                    <div>
                                        <label for="taskAssignee" class="block text-sm font-medium text-gray-700 mb-1">分配给</label>
                                        <select id="taskAssignee" name="assignee"
                                                class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">未分配</option>
                                            <!-- 用户选项将通过JavaScript动态加载 -->
                                        </select>
                                    </div>

                                    <div>
                                        <label for="taskStartDate" class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                                        <input type="date" id="taskStartDate" name="start_date"
                                               class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>

                                    <div>
                                        <label for="taskEndDate" class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                                        <input type="date" id="taskEndDate" name="end_date"
                                               class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>
                                </div>



                                <!-- 第三行：快捷时间选择（全宽，多列按钮） -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">快捷时间选择</label>
                                    <div id="timePresets" class="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2">
                                        <!-- 时间预设按钮将通过JavaScript动态加载 -->
                                    </div>
                                </div>

                                <!-- 第四行：富文本编辑器（全宽） -->
                                <div>
                                    <label for="taskDescription" class="block text-sm font-medium text-gray-700 mb-1">
                                        任务描述
                                        <span class="text-xs text-gray-500 font-normal ml-2">支持富文本格式、图片和附件上传</span>
                                    </label>
                                    <!-- 富文本编辑器整体容器 -->
                                    <div class="rich-text-wrapper">
                                        <!-- 富文本编辑器工具栏 -->
                                        <div id="toolbar-container" class="p-2">
                                            <div class="flex flex-wrap gap-1">
                                                <span class="ql-formats">
                                                    <button class="ql-bold"></button>
                                                    <button class="ql-italic"></button>
                                                    <button class="ql-underline"></button>
                                                    <button class="ql-strike"></button>
                                                </span>
                                                <span class="ql-formats">
                                                    <button class="ql-blockquote"></button>
                                                    <button class="ql-code-block"></button>
                                                </span>
                                                <span class="ql-formats">
                                                    <button class="ql-header" value="1"></button>
                                                    <button class="ql-header" value="2"></button>
                                                </span>
                                                <span class="ql-formats">
                                                    <button class="ql-list" value="ordered"></button>
                                                    <button class="ql-list" value="bullet"></button>
                                                </span>
                                                <span class="ql-formats">
                                                    <select class="ql-size">
                                                        <option value="small"></option>
                                                        <option selected></option>
                                                        <option value="large"></option>
                                                        <option value="huge"></option>
                                                    </select>
                                                </span>
                                                <span class="ql-formats">
                                                    <select class="ql-color"></select>
                                                    <select class="ql-background"></select>
                                                </span>
                                                <span class="ql-formats">
                                                    <button class="ql-link"></button>
                                                    <button class="ql-image"></button>
                                                    <button class="ql-attachment" title="插入附件"></button>
                                                </span>
                                                <span class="ql-formats">
                                                    <button class="ql-clean"></button>
                                                </span>
                                            </div>
                                        </div>
                                        <!-- 富文本编辑器内容区域 -->
                                        <div id="taskDescriptionEditor" style="height: 200px;"></div>
                                    </div>
                                    <textarea id="taskDescription" name="description" style="display: none;"></textarea>
                                    <div class="mt-1 text-xs text-gray-500">
                                        💡 提示：使用工具栏可以添加格式、链接、图片和附件。支持拖拽文件。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            保存
                        </button>
                        <button type="button" id="cancelBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            取消
                        </button>
                    </div>
                </form>
        </div>
    </div>

    <!-- 任务详情模态框 - 紧凑商务版 -->
    <div id="taskDetailModal" class="fixed inset-0 bg-gray-900 bg-opacity-60 hidden z-[9999] flex items-center justify-center p-3 backdrop-blur-sm">
        <div class="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[96vh] overflow-hidden relative z-[10000] border border-gray-200">

            <!-- 紧凑头部区域 -->
            <div class="bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-200 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2.5">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-base font-semibold text-gray-900 mb-0.5" id="detailModalTitle">任务详情</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500" id="detailTaskId">ID: #</span>
                                <span id="detailStatusBadge" class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium border"></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="closeDetailModal" class="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-all duration-200">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 紧凑内容区域 -->
            <div class="overflow-y-auto max-h-[calc(96vh-140px)]">
                <div class="p-4">
                    <!-- 紧凑任务标题区域 -->
                    <div class="mb-4">
                        <h2 id="detailTitle" class="text-lg font-bold text-gray-900 mb-2 leading-tight"></h2>
                        <div id="detailDescription" class="text-sm text-gray-700 leading-relaxed bg-gray-50 p-2.5 rounded-lg border border-gray-200"></div>
                    </div>

                    <!-- 紧凑核心信息卡片 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-4">
                        <!-- 任务状态卡片 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-2.5 shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-xs font-semibold text-gray-600 uppercase tracking-wide">任务状态</h4>
                                <svg class="w-2.5 h-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">当前状态</span>
                                    <span id="detailStatus" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">优先级</span>
                                    <span id="detailPriority" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">任务类型</span>
                                    <span id="detailType" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 人员信息卡片 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-2.5 shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-xs font-semibold text-gray-600 uppercase tracking-wide">人员信息</h4>
                                <svg class="w-2.5 h-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">分配给</span>
                                    <span id="detailAssignedUser" class="text-xs font-medium text-gray-900"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">创建者</span>
                                    <span id="detailCreator" class="text-xs text-gray-700"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 时间信息卡片 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-2.5 shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-xs font-semibold text-gray-600 uppercase tracking-wide">时间信息</h4>
                                <svg class="w-2.5 h-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">开始时间</span>
                                    <span id="detailStartDate" class="text-xs text-gray-900"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">结束时间</span>
                                    <span id="detailEndDate" class="text-xs text-gray-900"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">截止时间</span>
                                    <span id="detailDueDate" class="text-xs text-gray-900"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 紧凑进度统计区域 -->
                    <div class="bg-slate-50 border border-gray-200 rounded-lg p-2.5 mb-3">
                        <div class="flex items-center justify-between mb-2.5">
                            <h4 class="text-sm font-semibold text-gray-900">任务进度统计</h4>
                            <svg class="w-3.5 h-3.5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-2.5 mb-2.5">
                            <div class="text-center bg-white rounded-lg p-2.5 border border-gray-200">
                                <div class="text-xl font-bold text-blue-600 mb-1" id="detailEstimatedHours">0</div>
                                <div class="text-xs font-medium text-gray-600">预估工时 (小时)</div>
                            </div>
                            <div class="text-center bg-white rounded-lg p-2.5 border border-gray-200">
                                <div class="text-xl font-bold text-green-600 mb-1" id="detailActualHours">0</div>
                                <div class="text-xs font-medium text-gray-600">实际工时 (小时)</div>
                            </div>
                            <div class="text-center bg-white rounded-lg p-2.5 border border-gray-200">
                                <div class="text-xl font-bold text-purple-600 mb-1" id="detailProgressPercent">0%</div>
                                <div class="text-xs font-medium text-gray-600">完成进度</div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-2.5 border border-gray-200">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-xs font-medium text-gray-700">整体进度</span>
                                <span id="detailProgressText" class="text-xs font-semibold text-gray-900">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div id="detailProgressBar" class="bg-gradient-to-r from-blue-500 to-blue-600 h-1.5 rounded-full transition-all duration-500 shadow-sm" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 紧凑时间记录区域 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2.5 mb-3">
                        <div class="bg-white border border-gray-200 rounded-lg p-2.5">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2">创建信息</h5>
                            <div class="space-y-1">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">创建时间</span>
                                    <span id="detailCreatedAt" class="text-xs font-medium text-gray-900"></span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-2.5">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2">更新信息</h5>
                            <div class="space-y-1">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">更新时间</span>
                                    <span id="detailUpdatedAt" class="text-xs font-medium text-gray-900"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 紧凑附件区域 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-2.5 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-semibold text-gray-900">相关附件</h4>
                            <svg class="w-3.5 h-3.5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div id="detailAttachments" class="space-y-2">
                            <!-- 附件列表将在这里显示 -->
                            <div class="text-xs text-gray-500 italic">暂无附件</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 紧凑操作栏 -->
            <div class="bg-gray-50 border-t border-gray-200 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex space-x-1.5">
                        <button id="editFromDetail" class="inline-flex items-center px-2 py-1 border border-blue-500 text-xs font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-1 focus:ring-blue-400 transition-all duration-200">
                            <svg class="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            编辑
                        </button>
                        <button id="deleteFromDetail" class="inline-flex items-center px-2 py-1 border border-red-500 text-xs font-medium rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-1 focus:ring-red-400 transition-all duration-200">
                            <svg class="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            删除
                        </button>
                    </div>
                    <button type="button" id="closeDetailBtn" class="inline-flex items-center px-2.5 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-gray-400 transition-all duration-200">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[95vh] overflow-y-auto relative z-[10000]">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                系统设置
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- 通知设置 -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-gray-800">通知设置</h4>
                                    <div class="space-y-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enableNotifications" class="mr-2">
                                            <span class="text-sm">启用桌面通知</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enableTaskReminders" class="mr-2">
                                            <span class="text-sm">任务到期提醒</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enableAutoSave" class="mr-2">
                                            <span class="text-sm">自动保存草稿</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- 界面设置 -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-gray-800">界面设置</h4>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                                            <select id="themeSelect" class="w-full p-2 border border-gray-300 rounded">
                                                <option value="light">浅色主题</option>
                                                <option value="dark">深色主题</option>
                                                <option value="auto">跟随系统</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">动画效果</label>
                                            <select id="animationLevel" class="w-full p-2 border border-gray-300 rounded">
                                                <option value="full">完整动画</option>
                                                <option value="reduced">减少动画</option>
                                                <option value="none">禁用动画</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- 数据管理 -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-gray-800">数据管理</h4>
                                    <div class="space-y-3">
                                        <button onclick="createBackup()" class="w-full p-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                            创建备份
                                        </button>
                                        <button onclick="document.getElementById('restoreFile').click()" class="w-full p-2 bg-green-600 text-white rounded hover:bg-green-700">
                                            恢复备份
                                        </button>
                                        <input type="file" id="restoreFile" accept=".json" style="display: none;" onchange="restoreBackup(this.files[0])">
                                        <button onclick="generateAnalyticsReport()" class="w-full p-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                                            查看分析报告
                                        </button>
                                    </div>
                                </div>

                                <!-- 高级功能 -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-gray-800">高级功能</h4>
                                    <div class="space-y-3">
                                        <button onclick="openTaskTemplateManager()" class="w-full p-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                                            任务模板
                                        </button>
                                        <button onclick="showActivityFeed()" class="w-full p-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                                            活动记录
                                        </button>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enableOfflineMode" class="mr-2">
                                            <span class="text-sm">离线模式</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" id="saveSettingsBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        保存设置
                    </button>
                    <button type="button" id="closeSettingsBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
        </div>
    </div>


    <!-- 帮助模态框 -->
    <div id="helpModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[95vh] overflow-y-auto relative z-[10000]">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                使用帮助
                            </h3>
                            <div class="space-y-6">
                                <div>
                                    <h4 class="font-semibold text-gray-800 mb-2">快捷键</h4>
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <div class="flex justify-between">
                                            <span>新建任务</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl + N</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>显示帮助</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl + /</kbd>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>关闭模态框</span>
                                            <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Esc</kbd>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-800 mb-2">操作指南</h4>
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <p>• <strong>拖拽移动：</strong>直接拖拽任务卡片到不同列来改变状态</p>
                                        <p>• <strong>右键菜单：</strong>右键点击任务卡片显示快捷操作</p>
                                        <p>• <strong>搜索过滤：</strong>使用顶部搜索框和过滤器快速找到任务</p>
                                        <p>• <strong>主题切换：</strong>点击太阳/月亮图标切换明暗主题</p>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-800 mb-2">优先级说明</h4>
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                                            <span>🔴 高优先级 - 紧急重要任务</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                            <span>🟡 中优先级 - 常规任务</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                            <span>🟢 低优先级 - 可延后任务</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" id="closeHelpBtn" class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                        关闭
                    </button>
                </div>
        </div>
    </div>

    <!-- 任务类型管理模态框 -->
    <div id="taskTypeModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden" id="taskTypeModalContent">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white">任务类型管理</h3>
                            <p class="text-blue-100 text-sm">创建和管理您的项目任务分类</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 状态指示器 -->
                        <div id="typeManagerStatus" class="hidden">
                            <div class="flex items-center space-x-2 text-white text-sm">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span>已保存</span>
                            </div>
                        </div>
                        <button id="closeTaskTypeModalHeader" class="text-white hover:text-gray-200 transition-colors duration-200 p-2 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="px-6 py-5 overflow-y-auto max-h-[calc(90vh-100px)] bg-white">
                <!-- 类型列表区域 -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">任务类型管理</h4>
                            <p class="text-sm text-gray-600">共 <span id="typeCount">0</span> 个类型</p>
                        </div>
                        <button id="addNewTypeBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center font-medium shadow-sm hover:shadow-md">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新建类型
                        </button>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="flex flex-col sm:flex-row gap-4 mb-4">
                        <!-- 搜索框 -->
                        <div class="flex-1">
                            <div class="relative">
                                <input type="text" id="typeSearchInput" placeholder="搜索类型..."
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                <svg class="w-4 h-4 text-gray-400 absolute left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- 类型筛选 -->
                        <div class="flex space-x-2">
                            <button id="filterAll" class="px-3 py-2 text-sm rounded-lg bg-blue-100 text-blue-700 font-medium">全部</button>
                            <button id="filterSystem" class="px-3 py-2 text-sm rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors">系统</button>
                            <button id="filterCustom" class="px-3 py-2 text-sm rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors">自定义</button>
                        </div>
                    </div>

                    <!-- 类型列表 -->
                    <div id="taskTypesList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                        <!-- 加载状态 -->
                        <div id="typesLoading" class="col-span-full flex items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                        <!-- 类型列表将通过JavaScript动态加载 -->
                    </div>
                </div>

                    <!-- 类型编辑表单 -->
                    <div id="typeEditForm" class="hidden h-full overflow-y-auto">
                        <div class="p-6">
                            <!-- 表单头部 -->
                            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-900" id="typeFormTitle">新建类型</h4>
                                        <p class="text-sm text-gray-600">填写类型信息并保存</p>
                                    </div>
                                </div>
                                <button type="button" id="cancelTypeEdit" class="text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-lg hover:bg-gray-100">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <form id="taskTypeForm" class="space-y-6">
                                <input type="hidden" id="editingTypeId" value="">

                                <!-- 基本信息 -->
                                <div class="space-y-4">
                                    <h5 class="text-sm font-medium text-gray-900 flex items-center">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                        基本信息
                                    </h5>

                                    <div class="space-y-4">
                                        <div>
                                            <label for="typeName" class="block text-sm font-medium text-gray-700 mb-2">
                                                类型标识 <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="typeName" name="name" required maxlength="50"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                   placeholder="例如：feature, bug, task">
                                            <p class="text-xs text-gray-500 mt-1">英文标识符，用于系统内部识别</p>
                                            <div id="typeNameError" class="text-xs text-red-600 mt-1 hidden"></div>
                                        </div>

                                        <div>
                                            <label for="typeDisplayName" class="block text-sm font-medium text-gray-700 mb-2">
                                                显示名称 <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="typeDisplayName" name="display_name" required maxlength="100"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                   placeholder="例如：功能开发, 缺陷修复, 一般任务">
                                            <p class="text-xs text-gray-500 mt-1">用户界面中显示的名称</p>
                                            <div id="typeDisplayNameError" class="text-xs text-red-600 mt-1 hidden"></div>
                                        </div>
                                    </div>
                                    </div>

                                <!-- 外观设置 -->
                                <div class="space-y-4">
                                    <h5 class="text-sm font-medium text-gray-900 flex items-center">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                                        外观设置
                                    </h5>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="typeIcon" class="block text-sm font-medium text-gray-700 mb-2">
                                                图标 <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex items-center space-x-3">
                                                <!-- 图标显示 -->
                                                <div id="iconPreview" class="text-2xl w-12 h-12 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-50">🚀</div>
                                                <!-- 隐藏的输入框 -->
                                                <input type="hidden" id="typeIcon" name="icon" value="🚀" required>
                                                <!-- 选择按钮 -->
                                                <button type="button" onclick="console.log('按钮被点击'); window.openIconSelector ? window.openIconSelector() : alert('函数未找到');" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm font-medium">
                                                    选择图标
                                                </button>
                                            </div>

                                                <!-- 图标选择器 -->
                                                <div id="iconPicker" class="hidden border border-gray-300 rounded-lg bg-white shadow-lg p-4 max-h-80 overflow-hidden">
                                                    <!-- 搜索框 -->
                                                    <div class="mb-3">
                                                        <input type="text" id="iconSearchInput" placeholder="搜索图标..."
                                                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                               oninput="searchIcons(this.value)">
                                                    </div>

                                                    <!-- 图标分类标签 -->
                                                    <div class="flex space-x-2 mb-3 border-b border-gray-200 pb-2">
                                                        <button type="button" onclick="showIconCategory('work')" class="icon-category-btn px-2 py-1 text-xs rounded bg-blue-100 text-blue-700" data-category="work">工作</button>
                                                        <button type="button" onclick="showIconCategory('dev')" class="icon-category-btn px-2 py-1 text-xs rounded bg-gray-100 text-gray-600" data-category="dev">开发</button>
                                                        <button type="button" onclick="showIconCategory('ui')" class="icon-category-btn px-2 py-1 text-xs rounded bg-gray-100 text-gray-600" data-category="ui">界面</button>
                                                        <button type="button" onclick="showIconCategory('misc')" class="icon-category-btn px-2 py-1 text-xs rounded bg-gray-100 text-gray-600" data-category="misc">其他</button>
                                                    </div>

                                                    <!-- 图标网格 -->
                                                    <div class="overflow-y-auto max-h-48">
                                                        <div id="iconGrid" class="grid grid-cols-8 gap-2">
                                                            <!-- 图标将通过JavaScript动态加载 -->
                                                        </div>
                                                    </div>

                                                    <!-- 常用图标快捷选择 -->
                                                    <div class="mt-3 pt-3 border-t border-gray-200">
                                                        <div class="text-xs text-gray-500 mb-2">常用图标</div>
                                                        <div class="flex space-x-1">
                                                            <button type="button" onclick="selectIcon('📋')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">📋</button>
                                                            <button type="button" onclick="selectIcon('🚀')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">🚀</button>
                                                            <button type="button" onclick="selectIcon('🐛')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">🐛</button>
                                                            <button type="button" onclick="selectIcon('⚡')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">⚡</button>
                                                            <button type="button" onclick="selectIcon('🔧')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">🔧</button>
                                                            <button type="button" onclick="selectIcon('🎯')" class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 border border-gray-200 rounded transition-colors">🎯</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">点击选择图标或手动输入emoji</p>
                                        </div>

                                        <div>
                                            <label for="typeColor" class="block text-sm font-medium text-gray-700 mb-2">
                                                颜色 <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex items-center space-x-3">
                                                <input type="color" id="typeColor" name="color" required
                                                       class="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer"
                                                       value="#3B82F6">
                                                <input type="text" id="typeColorText"
                                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono transition-colors"
                                                       placeholder="#3B82F6" value="#3B82F6">
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">用于任务卡片的主题色</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 描述信息 -->
                                <div class="space-y-4">
                                    <h5 class="text-sm font-medium text-gray-900 flex items-center">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                        描述信息
                                    </h5>

                                    <div>
                                        <label for="typeDescription" class="block text-sm font-medium text-gray-700 mb-2">
                                            类型描述
                                        </label>
                                        <textarea id="typeDescription" name="description" rows="3"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"
                                                  placeholder="描述这个类型的用途和特点，让团队成员更好地理解使用场景"></textarea>
                                        <p class="text-xs text-gray-500 mt-1">可选字段，帮助团队更好地理解类型用途</p>
                                    </div>
                                </div>

                                <!-- 按钮组 -->
                                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                                    <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium">
                                        保存类型
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-5 border-t border-blue-100">
                <!-- <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-700 flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">温馨提示</div>
                            <div class="text-xs text-gray-600">系统类型受保护不能删除，但可以修改显示样式</div>
                        </div> 
                    </div>
                    <button type="button" id="closeTaskTypeModal" class="px-5 py-2.5 bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-700 rounded-xl transition-all duration-300 font-semibold shadow-md hover:shadow-lg flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        关闭
                    </button>
                </div> -->
            </div>
        </div>
    </div>

    <!-- 任务版本管理模态框 -->
    <div id="taskVersionModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden" id="taskVersionModalContent">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white">版本管理</h3>
                            <p class="text-purple-100 text-sm">管理项目版本信息</p>
                        </div>
                    </div>
                    <button id="closeTaskVersionModalHeader" class="text-white hover:text-purple-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="px-6 py-5 overflow-y-auto max-h-[calc(90vh-100px)] bg-white">
                <!-- 版本列表区域 -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">任务版本管理</h4>
                            <p class="text-sm text-gray-600">共 <span id="versionCount">0</span> 个版本</p>
                        </div>
                        <button id="addNewVersionBtn" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center font-medium shadow-sm hover:shadow-md">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新建版本
                        </button>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="flex space-x-2 mb-4">
                        <button id="filterAllVersions" class="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium transition-colors">
                            全部
                        </button>
                        <button id="filterActiveVersions" class="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium transition-colors">
                            激活
                        </button>
                        <button id="filterInactiveVersions" class="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium transition-colors">
                            停用
                        </button>
                    </div>

                    <!-- 版本列表 -->
                    <div id="taskVersionsList" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <!-- 版本卡片将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 版本编辑表单区域 -->
                <div id="versionEditForm" class="hidden bg-gray-50 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h5 id="versionFormTitle" class="text-lg font-semibold text-gray-900">新建版本</h5>
                        <button id="cancelVersionEdit" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form id="taskVersionForm" class="space-y-4">
                        <input type="hidden" id="editingVersionId" name="id">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="versionName" class="block text-sm font-medium text-gray-700 mb-1">版本标识 <span class="text-red-500">*</span></label>
                                <input type="text" id="versionName" name="name" required maxlength="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="如：v1.0.0">
                            </div>
                            <div>
                                <label for="versionDisplayName" class="block text-sm font-medium text-gray-700 mb-1">显示名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="versionDisplayName" name="display_name" required maxlength="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="如：版本 1.0.0">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="versionIcon" class="block text-sm font-medium text-gray-700 mb-1">图标 <span class="text-red-500">*</span></label>
                                <div class="flex items-center space-x-2">
                                    <input type="text" id="versionIcon" name="icon" required maxlength="10"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                           placeholder="📋">
                                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-lg" id="versionIconPreview">📋</div>
                                    <button type="button" class="px-3 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors text-sm" data-action="toggle-version-icon-picker">
                                        选择
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label for="versionColor" class="block text-sm font-medium text-gray-700 mb-1">颜色 <span class="text-red-500">*</span></label>
                                <div class="flex items-center space-x-2">
                                    <input type="color" id="versionColor" name="color" required
                                           class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer">
                                    <input type="text" id="versionColorText"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                           placeholder="#3B82F6" value="#3B82F6">
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="versionDescription" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                            <textarea id="versionDescription" name="description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                      placeholder="版本描述信息（可选）"></textarea>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4">
                            <button type="button" id="cancelVersionEditBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                保存版本
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 版本编辑占位符 -->
                <div id="versionEditPlaceholder" class="text-center py-12 text-gray-500">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    <p class="text-lg font-medium mb-2">选择版本进行编辑</p>
                    <p class="text-sm">点击左侧版本卡片或创建新版本</p>
                </div>

                <!-- 底部操作区域 -->
                <!-- <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-3 text-sm text-gray-600">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span>激活版本</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span>停用版本</span>
                        </div>
                    </div>
                  <div>
                        <div class="font-semibold text-gray-800">温馨提示</div>
                        <div class="text-xs text-gray-600">停用版本不会影响已关联的任务</div>
                    </div> 
                    <button type="button" id="closeTaskVersionModal" class="px-5 py-2.5 bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-700 rounded-xl transition-all duration-300 font-semibold shadow-md hover:shadow-lg flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        关闭
                    </button>
                </div> -->
            </div>
        </div>
    </div>

    <!-- 启动加载屏幕 -->
    <div id="loadingScreen" class="fixed inset-0 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 mx-auto animate-pulse">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">ProjectM</h2>
            <p class="text-gray-600 mb-6">高效项目管理系统</p>
            <div class="flex justify-center">
                <div class="loading-spinner"></div>
            </div>
            <p class="text-sm text-gray-500 mt-4">正在加载...</p>
        </div>
    </div>

    <!-- 自定义JavaScript -->
    <script src="/static/js/app.js?v=28"></script>
    <script src="/static/js/task-types.js?v=7"></script>
    <script src="/static/js/task-versions.js?v=1"></script>
    <script src="/static/js/task-templates-test.js?v=21&t=**********"></script>

    <!-- 图标选择器临时修复 -->
    <script>
        // 临时图标选择器函数
        window.openIconSelector = function() {
            console.log('🎯 === 临时图标选择器被调用 ===');

            // 移除已存在的选择器
            const existingPicker = document.getElementById('simpleIconPicker');
            if (existingPicker) {
                existingPicker.remove();
            }

            const iconInput = document.getElementById('typeIcon');
            const iconPreview = document.getElementById('iconPreview');

            if (!iconInput || !iconPreview) {
                alert('找不到图标输入框或预览框');
                return;
            }

            // 创建选择器
            const picker = document.createElement('div');
            picker.id = 'simpleIconPicker';
            picker.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                padding: 20px;
                z-index: 10000;
                max-width: 400px;
                width: 90vw;
            `;

            const commonIcons = [
                '📋', '🚀', '🐛', '⚡', '🔧', '🎯',
                '📊', '📝', '💡', '🔍', '⚙️', '🛠️',
                '🎨', '📅', '💼', '📞', '📧', '🔒',
                '🔑', '💾', '🌟', '🎉', '🔥', '💎'
            ];

            picker.innerHTML = `
                <div style="margin-bottom: 16px;">
                    <h3 style="font-size: 16px; font-weight: 600; color: #374151; margin-bottom: 12px;">选择图标</h3>
                    <div style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px;">
                        ${commonIcons.map(icon => `
                            <button type="button"
                                    onclick="selectIcon('${icon}')"
                                    style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 1px solid #d1d5db; border-radius: 8px; background: white; cursor: pointer; transition: all 0.2s;"
                                    onmouseover="this.style.background='#eff6ff'; this.style.borderColor='#3b82f6';"
                                    onmouseout="this.style.background='white'; this.style.borderColor='#d1d5db';"
                                    title="选择 ${icon}">
                                ${icon}
                            </button>
                        `).join('')}
                    </div>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button type="button"
                            onclick="document.getElementById('simpleIconPicker').remove(); document.getElementById('pickerOverlay').remove();"
                            style="flex: 1; padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;">
                        关闭
                    </button>
                    <button type="button"
                            onclick="selectIcon('🚀')"
                            style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;">
                        默认 🚀
                    </button>
                </div>
            `;

            // 添加到body
            document.body.appendChild(picker);

            // 添加背景遮罩
            const overlay = document.createElement('div');
            overlay.id = 'pickerOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9999;
            `;
            overlay.onclick = function() {
                picker.remove();
                overlay.remove();
            };
            document.body.appendChild(overlay);
        };

        // 选择图标函数
        window.selectIcon = function(icon) {
            console.log('🎯 选择图标:', icon);

            const iconInput = document.getElementById('typeIcon');
            const iconPreview = document.getElementById('iconPreview');

            if (iconInput) {
                iconInput.value = icon;
            }

            if (iconPreview) {
                iconPreview.textContent = icon;
            }

            // 关闭选择器
            const picker = document.getElementById('simpleIconPicker');
            const overlay = document.getElementById('pickerOverlay');
            if (picker) picker.remove();
            if (overlay) overlay.remove();
        };
    </script>

    <!-- 责任人选择器临时修复 -->
    <script>
        // 临时全局函数定义，确保onclick可以访问
        window.showAssigneeSelector = function() {
            console.log('临时showAssigneeSelector被调用');
            const modal = document.getElementById('assigneeSelectorModal');
            if (modal) {
                modal.classList.remove('hidden');
                // 调用app.js中的loadAssigneeList函数
                if (window.loadAssigneeList) {
                    window.loadAssigneeList();
                } else {
                    console.error('loadAssigneeList函数未找到');
                }
            } else {
                console.error('assigneeSelectorModal元素未找到');
            }
        };

        window.closeAssigneeSelector = function() {
            console.log('临时closeAssigneeSelector被调用');
            const modal = document.getElementById('assigneeSelectorModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        };

        window.clearAssigneeFilter = function(event) {
            console.log('临时clearAssigneeFilter被调用');
            if (event) {
                event.stopPropagation();
            }
            if (window.selectAssignee) {
                window.selectAssignee(null);
            }
        };

        // 确保本地存储函数也可用
        window.loadAssigneeList = function() {
            console.log('临时loadAssigneeList被调用');
            fetch('/api/users/active')
                .then(response => response.json())
                .then(data => {
                    if (data.users && window.displayAssigneeList) {
                        window.displayAssigneeList(data.users);
                    } else {
                        console.error('displayAssigneeList函数未找到或用户数据为空');
                    }
                })
                .catch(error => {
                    console.error('加载用户列表失败:', error);
                });
        };
    </script>

    <!-- 隐藏加载屏幕 -->
    <script>
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.5s ease';
                setTimeout(function() {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        });

        // 用户界面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化用户下拉菜单
            initUserDropdown();

            // 初始化个人设置模态框
            initPersonalSettingsModal();

            // 加载当前用户信息
            loadCurrentUserInfo();
        });

        // 初始化用户下拉菜单
        function initUserDropdown() {
            const userInfoBtn = document.getElementById('currentUserInfo');
            const userDropdown = document.getElementById('userDropdown');
            const personalSettingsBtn = document.getElementById('personalSettingsBtn');
            const logoutBtnDropdown = document.getElementById('logoutBtnDropdown');

            // 点击用户信息显示/隐藏下拉菜单
            if (userInfoBtn) {
                userInfoBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    userDropdown.classList.toggle('hidden');
                });
            }

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!userInfoBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.add('hidden');
                }
            });

            // 个人设置按钮
            if (personalSettingsBtn) {
                personalSettingsBtn.addEventListener('click', function() {
                    userDropdown.classList.add('hidden');
                    document.getElementById('personalSettingsModal').classList.remove('hidden');
                });
            }

            // 下拉菜单中的退出登录按钮
            if (logoutBtnDropdown) {
                logoutBtnDropdown.addEventListener('click', function() {
                    handleLogout();
                });
            }
        }

        // 处理退出登录
        function handleLogout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 退出成功，跳转到登录页
                        window.location.href = '/login';
                    } else {
                        alert('退出失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('退出登录错误:', error);
                    // 即使出错也跳转到登录页，因为可能是网络问题
                    window.location.href = '/login';
                });
            }
        }

        // 初始化个人设置模态框
        function initPersonalSettingsModal() {
            const modal = document.getElementById('personalSettingsModal');
            const closeBtn = document.getElementById('closePersonalSettingsModal');
            const cancelBtn = document.getElementById('cancelPasswordChange');
            const form = document.getElementById('changePasswordForm');

            // 关闭模态框
            function closeModal() {
                modal.classList.add('hidden');
                form.reset();
                hidePasswordChangeMessage();
            }

            // 绑定关闭按钮事件
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeModal);
            }

            // 点击模态框外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
            });

            // 表单提交处理
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handlePasswordChange();
                });
            }

            // 添加实时密码强度检测
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    checkPasswordStrength(this.value);
                    validatePasswordMatch();
                });
            }

            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    validatePasswordMatch();
                });
            }
        }

        // 检查密码强度
        function checkPasswordStrength(password) {
            const strengthDiv = document.getElementById('passwordStrength');
            const strengthBar = document.getElementById('strengthBar');
            const strengthText = document.getElementById('strengthText');

            if (!password) {
                strengthDiv.classList.add('hidden');
                return;
            }

            strengthDiv.classList.remove('hidden');

            let strength = 0;
            let feedback = '';

            // 长度检查
            if (password.length >= 6) strength += 1;
            if (password.length >= 8) strength += 1;

            // 复杂度检查
            if (/[a-z]/.test(password)) strength += 1;
            if (/[A-Z]/.test(password)) strength += 1;
            if (/[0-9]/.test(password)) strength += 1;
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;

            // 设置强度显示
            const maxStrength = 6;
            const percentage = (strength / maxStrength) * 100;

            if (strength <= 2) {
                strengthBar.className = 'h-1 rounded transition-all duration-300 bg-red-500';
                strengthText.textContent = '弱';
                strengthText.className = 'text-red-500';
            } else if (strength <= 4) {
                strengthBar.className = 'h-1 rounded transition-all duration-300 bg-yellow-500';
                strengthText.textContent = '中等';
                strengthText.className = 'text-yellow-600';
            } else {
                strengthBar.className = 'h-1 rounded transition-all duration-300 bg-green-500';
                strengthText.textContent = '强';
                strengthText.className = 'text-green-600';
            }

            strengthBar.style.width = `${percentage}%`;
        }

        // 验证密码匹配
        function validatePasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const confirmInput = document.getElementById('confirmPassword');

            if (!confirmPassword) {
                confirmInput.classList.remove('border-red-500', 'border-green-500');
                return;
            }

            if (newPassword === confirmPassword) {
                confirmInput.classList.remove('border-red-500');
                confirmInput.classList.add('border-green-500');
            } else {
                confirmInput.classList.remove('border-green-500');
                confirmInput.classList.add('border-red-500');
            }
        }

        // 处理修改密码
        function handlePasswordChange() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const submitBtn = document.getElementById('submitPasswordChange');
            const submitText = document.getElementById('submitPasswordText');
            const submitSpinner = document.getElementById('submitPasswordSpinner');

            // 表单验证
            if (!currentPassword || !newPassword || !confirmPassword) {
                showPasswordChangeMessage('请填写所有字段', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showPasswordChangeMessage('新密码长度至少6位', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showPasswordChangeMessage('两次输入的新密码不一致', 'error');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            submitText.textContent = '修改中...';
            submitSpinner.classList.remove('hidden');

            // 发送请求
            fetch('/api/auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    old_password: currentPassword,
                    new_password: newPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showPasswordChangeMessage('密码修改成功！', 'success');
                    // 3秒后关闭模态框
                    setTimeout(() => {
                        document.getElementById('personalSettingsModal').classList.add('hidden');
                        document.getElementById('changePasswordForm').reset();
                        hidePasswordChangeMessage();
                    }, 2000);
                } else {
                    showPasswordChangeMessage(data.message || '修改失败', 'error');
                }
            })
            .catch(error => {
                console.error('修改密码错误:', error);
                showPasswordChangeMessage('网络错误，请稍后重试', 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitText.textContent = '修改密码';
                submitSpinner.classList.add('hidden');
            });
        }

        // 显示密码修改消息
        function showPasswordChangeMessage(message, type) {
            const messageDiv = document.getElementById('passwordChangeMessage');
            messageDiv.textContent = message;
            messageDiv.className = `p-3 rounded-lg text-sm ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            messageDiv.classList.remove('hidden');
        }

        // 隐藏密码修改消息
        function hidePasswordChangeMessage() {
            const messageDiv = document.getElementById('passwordChangeMessage');
            messageDiv.classList.add('hidden');
        }

        // 加载当前用户信息
        function loadCurrentUserInfo() {
            fetch('/api/auth/user')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.user) {
                        updateUserDisplay(data.user);
                    } else {
                        console.error('获取用户信息失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('获取用户信息错误:', error);
                });
        }

        // 更新用户显示
        function updateUserDisplay(user) {
            const userAvatar = document.getElementById('userAvatar');
            const userDisplayName = document.getElementById('userDisplayName');
            const userRole = document.getElementById('userRole');
            const userManagementBtn = document.getElementById('userManagementBtn');

            if (userAvatar && user.full_name) {
                userAvatar.textContent = user.full_name.charAt(0).toUpperCase();
            }

            if (userDisplayName) {
                userDisplayName.textContent = user.full_name || user.username || '未知用户';
            }

            if (userRole) {
                const roleNames = {
                    'admin': '管理员',
                    'manager': '项目经理',
                    'developer': '开发者',
                    'viewer': '查看者'
                };
                userRole.textContent = roleNames[user.role] || user.role || '用户';
            }

            // 权限控制：只有管理员才能看到用户管理按钮
            if (userManagementBtn) {
                if (user.role === 'admin') {
                    userManagementBtn.classList.remove('hidden');
                } else {
                    userManagementBtn.classList.add('hidden');
                }
            }

            // 权限控制：只有管理员才能看到模板管理和类型管理按钮
            const templateManagementBtn = document.getElementById('templateManagementBtn');
            const typeManagementBtn = document.getElementById('typeManagementBtn');
            const quickTemplateManageBtn = document.getElementById('quickTemplateManageBtn');

            // 快速操作区的模板管理按钮 - 仅管理员可见
            if (templateManagementBtn) {
                if (user.role === 'admin') {
                    templateManagementBtn.classList.remove('hidden');
                } else {
                    templateManagementBtn.classList.add('hidden');
                }
            }

            // 类型管理按钮 - 仅管理员可见
            if (typeManagementBtn) {
                if (user.role === 'admin') {
                    typeManagementBtn.classList.remove('hidden');
                } else {
                    typeManagementBtn.classList.add('hidden');
                }
            }

            // 版本管理按钮 - 仅管理员可见
            const versionManagementBtn = document.getElementById('versionManagementBtn');
            if (versionManagementBtn) {
                if (user.role === 'admin') {
                    versionManagementBtn.classList.remove('hidden');
                } else {
                    versionManagementBtn.classList.add('hidden');
                }
            }

            // 快速模板选择器中的管理按钮 - 仅管理员可见
            if (quickTemplateManageBtn) {
                if (user.role === 'admin') {
                    quickTemplateManageBtn.classList.remove('hidden');
                } else {
                    quickTemplateManageBtn.classList.add('hidden');
                }
            }

            // 模板管理器中的新建模板按钮 - 仅管理员可见
            const addNewTemplateBtn = document.getElementById('addNewTemplateBtn');
            if (addNewTemplateBtn) {
                if (user.role === 'admin') {
                    addNewTemplateBtn.classList.remove('hidden');
                } else {
                    addNewTemplateBtn.classList.add('hidden');
                }
            }

            // 注意：快速模板选择器本身对所有用户可见，只是管理功能受限
        }

        // 🎯 直接的按钮处理函数
        function handleExportClick() {
            console.log('🎯 导出按钮被点击了！');

            // 移除现有菜单
            const existingMenu = document.getElementById('exportMenu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // 获取按钮位置
            const exportBtn = document.getElementById('exportBtn');
            const rect = exportBtn.getBoundingClientRect();

            // 创建导出选项菜单
            const exportMenu = document.createElement('div');
            exportMenu.id = 'exportMenu';
            exportMenu.className = 'fixed bg-white rounded-lg shadow-xl border border-gray-200 z-[99999] w-44';
            exportMenu.style.left = rect.left + 'px';
            exportMenu.style.top = (rect.bottom + 4) + 'px';
            exportMenu.innerHTML = `
                <div class="py-1">
                    <button onclick="exportToCSV()" class="w-full text-left px-3 py-2 hover:bg-gray-50 transition-colors text-sm flex items-center space-x-2">
                        <span>📊</span><span>导出为 CSV</span>
                    </button>
                    <button onclick="exportToJSON()" class="w-full text-left px-3 py-2 hover:bg-gray-50 transition-colors text-sm flex items-center space-x-2">
                        <span>📄</span><span>导出为 JSON</span>
                    </button>
                    <button onclick="exportToMarkdown()" class="w-full text-left px-3 py-2 hover:bg-gray-50 transition-colors text-sm flex items-center space-x-2">
                        <span>📝</span><span>导出为 Markdown</span>
                    </button>
                </div>
            `;

            // 添加到body
            document.body.appendChild(exportMenu);

            // 点击外部关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeExportMenu(e) {
                    if (!exportMenu.contains(e.target) && !document.getElementById('exportBtn').contains(e.target)) {
                        exportMenu.remove();
                        document.removeEventListener('click', closeExportMenu);
                    }
                }, true);
            }, 100);

            // 点击外部关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeMenu(e) {
                    if (!exportMenu.contains(e.target) && e.target !== exportBtn) {
                        exportMenu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }, 100);
        }











        // 批量移动处理
        function handleBulkMove() {
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox input[type="checkbox"]:checked');

            if (selectedCheckboxes.length === 0) {
                showToast('请先选择要移动的任务', 'warning');
                return;
            }

            // 创建状态选择菜单 - 现代化设计
            const moveMenu = document.createElement('div');
            moveMenu.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] backdrop-blur-sm';
            moveMenu.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-100">
                    <div class="text-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">移动任务</h3>
                        <p class="text-sm text-gray-500 mt-1">选择目标状态</p>
                    </div>

                    <div class="space-y-3">
                        <button onclick="moveTasks('todo')" class="w-full text-left p-4 rounded-xl border-2 border-gray-200 hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900">待办事项</div>
                                <div class="text-sm text-gray-500">等待开始的任务</div>
                            </div>
                        </button>

                        <button onclick="moveTasks('in_progress')" class="w-full text-left p-4 rounded-xl border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900">进行中</div>
                                <div class="text-sm text-gray-500">正在执行的任务</div>
                            </div>
                        </button>

                        <button onclick="moveTasks('completed')" class="w-full text-left p-4 rounded-xl border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200 flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900">已完成</div>
                                <div class="text-sm text-gray-500">完成的任务</div>
                            </div>
                        </button>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                        <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium rounded-lg hover:bg-gray-100 transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(moveMenu);
        }

        // 移动任务到指定状态
        function moveTasks(status) {
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox input[type="checkbox"]:checked');
            const taskIds = Array.from(selectedCheckboxes).map(checkbox => {
                return checkbox.closest('.task-card')?.dataset.taskId;
            }).filter(id => id);

            console.log('批量移动任务到:', status, taskIds);

            // 调用API进行批量状态更新
            fetch('/api/tasks/bulk/status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_ids: taskIds,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`成功移动 ${taskIds.length} 个任务`, 'success');
                    // 刷新页面
                    location.reload();
                } else {
                    showToast('移动失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('批量移动失败:', error);
                showToast('移动失败', 'error');
            });

            // 关闭移动菜单
            document.querySelector('.fixed').remove();
        }

        // 导出功能实现
        function exportToCSV() {
            console.log('开始导出CSV...');
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    const tasks = data.tasks || [];
                    const csvContent = generateCSV(tasks);
                    downloadFile(csvContent, 'tasks.csv', 'text/csv');
                    showToast('CSV导出成功', 'success');
                })
                .catch(error => {
                    console.error('导出失败:', error);
                    showToast('导出失败', 'error');
                });

            // 关闭菜单
            const menu = document.getElementById('exportMenu');
            if (menu) menu.remove();
        }

        function exportToJSON() {
            console.log('开始导出JSON...');
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    const jsonContent = JSON.stringify(data.tasks || [], null, 2);
                    downloadFile(jsonContent, 'tasks.json', 'application/json');
                    showToast('JSON导出成功', 'success');
                })
                .catch(error => {
                    console.error('导出失败:', error);
                    showToast('导出失败', 'error');
                });

            // 关闭菜单
            const menu = document.getElementById('exportMenu');
            if (menu) menu.remove();
        }

        function exportToMarkdown() {
            console.log('开始导出Markdown...');
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    const tasks = data.tasks || [];
                    const markdownContent = generateMarkdown(tasks);
                    downloadFile(markdownContent, 'tasks.md', 'text/markdown');
                    showToast('Markdown导出成功', 'success');
                })
                .catch(error => {
                    console.error('导出失败:', error);
                    showToast('导出失败', 'error');
                });

            // 关闭菜单
            const menu = document.getElementById('exportMenu');
            if (menu) menu.remove();
        }

        // 生成CSV内容
        function generateCSV(tasks) {
            const headers = ['标题', '描述', '状态', '优先级', '创建时间', '截止时间'];
            const rows = [headers.join(',')];

            tasks.forEach(task => {
                const row = [
                    `"${task.title || ''}"`,
                    `"${task.description || ''}"`,
                    `"${task.status || ''}"`,
                    `"${task.priority || ''}"`,
                    `"${task.created_at || ''}"`,
                    `"${task.due_date || ''}"`
                ];
                rows.push(row.join(','));
            });

            return rows.join('\n');
        }

        // 生成Markdown内容
        function generateMarkdown(tasks) {
            let markdown = '# 任务列表\n\n';

            tasks.forEach(task => {
                markdown += `## ${task.title || '无标题'}\n\n`;
                markdown += `- **状态**: ${task.status || '未知'}\n`;
                markdown += `- **优先级**: ${task.priority || '未设置'}\n`;
                markdown += `- **创建时间**: ${task.created_at || '未知'}\n`;
                if (task.due_date) {
                    markdown += `- **截止时间**: ${task.due_date}\n`;
                }
                if (task.description) {
                    markdown += `\n${task.description}\n`;
                }
                markdown += '\n---\n\n';
            });

            return markdown;
        }

        // 下载文件
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>

    <!-- 任务模板管理模态框 -->
    <div id="taskTemplateModal" class="fixed inset-0 bg-black bg-opacity-70 hidden z-[9999] flex items-center justify-center p-4 backdrop-blur-md">
        <div class="bg-white rounded-3xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden relative z-[10000] transform transition-all duration-500 scale-95 opacity-0" id="taskTemplateModalContent">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 px-8 py-6 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

                <div class="flex items-center justify-between relative z-10">
                    <div class="flex items-center space-x-4">
                        <div class="text-4xl drop-shadow-lg animate-pulse">📋</div>
                        <div>
                            <h3 class="text-2xl font-bold text-white drop-shadow-lg">任务模板管理</h3>
                            <p class="text-white/80 text-sm mt-1">管理您的任务模板，提高工作效率</p>
                        </div>
                    </div>
                    <button id="closeTaskTemplateModalHeader" class="text-white hover:text-gray-200 transition-all duration-300 p-3 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 装饰性波浪 -->
                <div class="absolute bottom-0 left-0 right-0">
                    <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="w-full h-4 text-white opacity-30">
                        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".4" fill="currentColor"></path>
                        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".6" fill="currentColor"></path>
                        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="currentColor"></path>
                    </svg>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="px-8 py-8 overflow-y-auto max-h-[calc(95vh-140px)] bg-gradient-to-br from-gray-50/50 to-white">
                <div class="w-full">

                    <!-- 模板列表区域 -->
                    <div class="mb-10">
                        <div class="flex justify-between items-center mb-8">
                            <div>
                                <h4 class="text-2xl font-bold text-gray-800 flex items-center mb-2">
                                    <span class="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-4"></span>
                                    现有模板
                                </h4>
                                <p class="text-gray-600 ml-6">选择模板快速创建任务，或管理您的自定义模板</p>
                            </div>
                            <button id="addNewTemplateBtn" class="hidden px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-2xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-semibold">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新建模板
                            </button>
                        </div>
                        <div id="taskTemplatesList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-h-96 overflow-y-auto pr-2 custom-scrollbar">
                            <!-- 模板列表将通过JavaScript动态加载 -->
                        </div>
                    </div>

                    <!-- 模板编辑表单 -->
                    <div id="templateEditForm" class="hidden">
                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-3xl p-8 border border-gray-200 shadow-lg">
                            <div class="flex items-center mb-8">
                                <span class="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-4"></span>
                                <h4 class="text-2xl font-bold text-gray-800" id="templateFormTitle">新建模板</h4>
                            </div>
                            <form id="taskTemplateForm" class="space-y-6">
                                <input type="hidden" id="editingTemplateId" value="">

                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div class="space-y-2">
                                        <label for="templateName" class="block text-sm font-semibold text-gray-700 flex items-center">
                                            <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                            模板名称 <span class="text-red-500 ml-1">*</span>
                                        </label>
                                        <input type="text" id="templateName" name="name" required maxlength="100"
                                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 bg-white hover:border-gray-300"
                                               placeholder="例如：🐛 Bug修复模板">
                                    </div>

                                    <div class="space-y-2">
                                        <label for="templateDescription" class="block text-sm font-semibold text-gray-700 flex items-center">
                                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                            模板描述
                                        </label>
                                        <input type="text" id="templateDescription" name="description" maxlength="200"
                                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 bg-white hover:border-gray-300"
                                               placeholder="描述模板的用途和适用场景">
                                    </div>

                                    <div class="space-y-2">
                                        <label for="templatePriority" class="block text-sm font-semibold text-gray-700 flex items-center">
                                            <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                                            默认优先级
                                        </label>
                                        <select id="templatePriority" name="priority"
                                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-300 bg-white hover:border-gray-300">
                                            <option value="low">🟢 低优先级</option>
                                            <option value="medium" selected>🟡 中优先级</option>
                                            <option value="high">🔴 高优先级</option>
                                        </select>
                                    </div>

                                    <div class="space-y-2">
                                        <label for="templateType" class="block text-sm font-semibold text-gray-700 flex items-center">
                                            <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                            默认类型
                                        </label>
                                        <select id="templateType" name="type"
                                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 bg-white hover:border-gray-300">
                                            <option value="task">📋 一般任务</option>
                                            <option value="feature">🚀 功能开发</option>
                                            <option value="bug">🐛 缺陷修复</option>
                                            <option value="improvement">⚡ 改进优化</option>
                                            <option value="research">🔍 研究调研</option>
                                            <option value="testing">🧪 测试</option>
                                            <option value="documentation">📚 文档</option>
                                            <option value="maintenance">🔧 维护</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="templateTitle" class="block text-sm font-semibold text-gray-700 flex items-center">
                                        <span class="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                                        默认标题 <span class="text-red-500 ml-1">*</span>
                                    </label>
                                    <input type="text" id="templateTitle" name="title" required maxlength="255"
                                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 bg-white hover:border-gray-300"
                                           placeholder="例如：修复：[描述问题]">
                                </div>

                                <div class="space-y-2">
                                    <label for="templateContent" class="block text-sm font-semibold text-gray-700 flex items-center">
                                        <span class="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                                        默认内容
                                    </label>
                                    <textarea id="templateContent" name="content" rows="8"
                                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-all duration-300 bg-white hover:border-gray-300 resize-none"
                                              placeholder="输入任务的默认内容模板，支持Markdown格式"></textarea>
                                </div>

                                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                                    <button type="button" id="cancelTemplateEdit" class="px-6 py-3 border-2 border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-medium">
                                        取消
                                    </button>
                                    <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                        保存模板
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="bg-gradient-to-r from-gray-50 via-white to-gray-50 px-8 py-6 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-600 flex items-center">
                        <span class="text-lg mr-2">💡</span>
                        <span>使用模板可以快速创建标准化的任务，提高工作效率</span>
                    </div>
                    <button type="button" id="closeTaskTemplateModal" class="px-8 py-3 bg-gradient-to-r from-gray-100 to-gray-200 border-2 border-gray-300 rounded-2xl text-gray-700 hover:from-gray-200 hover:to-gray-300 hover:border-gray-400 transition-all duration-300 font-semibold shadow-sm hover:shadow-md">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人设置模态框 -->
    <div id="personalSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999] flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full relative z-[10000]">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">个人设置</h3>
                            <p class="text-blue-100 text-sm">修改您的账户信息</p>
                        </div>
                    </div>
                    <button id="closePersonalSettingsModal" class="text-white hover:text-blue-200 transition-colors p-1 rounded">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <!-- 修改密码表单 -->
                <form id="changePasswordForm" class="space-y-4">
                    <div>
                        <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                        <input
                            type="password"
                            id="currentPassword"
                            name="currentPassword"
                            autocomplete="current-password"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="请输入当前密码"
                        >
                    </div>

                    <div>
                        <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                        <input
                            type="password"
                            id="newPassword"
                            name="newPassword"
                            autocomplete="new-password"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="请输入新密码（至少6位）"
                        >
                        <div id="passwordStrength" class="mt-1 text-xs hidden">
                            <div class="flex items-center space-x-1">
                                <div class="flex-1 h-1 bg-gray-200 rounded">
                                    <div id="strengthBar" class="h-1 rounded transition-all duration-300"></div>
                                </div>
                                <span id="strengthText" class="text-gray-500"></span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                        <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            autocomplete="new-password"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="请再次输入新密码"
                        >
                    </div>

                    <!-- 错误/成功消息 -->
                    <div id="passwordChangeMessage" class="hidden p-3 rounded-lg text-sm"></div>

                    <!-- 按钮组 -->
                    <div class="flex space-x-3 pt-4">
                        <button
                            type="button"
                            id="cancelPasswordChange"
                            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            id="submitPasswordChange"
                            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span id="submitPasswordText">修改密码</span>
                            <div id="submitPasswordSpinner" class="hidden inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 责任人选择模态框 -->
    <div id="assigneeSelectorModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">选择责任人</h3>
                <button onclick="closeAssigneeSelector()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="p-4">
                <!-- 搜索框 -->
                <div class="relative mb-4">
                    <input type="text" id="assigneeSearchInput" placeholder="搜索用户..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- 清除选择按钮 -->
                <div class="mb-4">
                    <button onclick="selectAssignee(null)" class="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">清除选择</div>
                            <div class="text-sm text-gray-500">显示所有任务</div>
                        </div>
                    </button>
                </div>

                <!-- 用户列表 -->
                <div id="assigneeList" class="space-y-2 max-h-64 overflow-y-auto">
                    <!-- 用户列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
</body>
</html>
