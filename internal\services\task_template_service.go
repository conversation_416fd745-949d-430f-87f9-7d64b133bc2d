package services

import (
	"errors"
	"fmt"
	"projectm2/internal/models"

	"gorm.io/gorm"
)

// TaskTemplateService 任务模板服务
type TaskTemplateService struct {
	db *gorm.DB
}

// NewTaskTemplateService 创建新的任务模板服务实例
func NewTaskTemplateService(db *gorm.DB) *TaskTemplateService {
	return &TaskTemplateService{db: db}
}

// GetAllTaskTemplates 获取所有任务模板
func (s *TaskTemplateService) GetAllTaskTemplates() ([]models.TaskTemplateResponse, error) {
	var templates []models.TaskTemplate
	err := s.db.Preload("Creator").Where("is_active = ?", true).Order("sort_order ASC, created_at ASC").Find(&templates).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var responses []models.TaskTemplateResponse
	for _, template := range templates {
		responses = append(responses, models.TaskTemplateResponse{
			ID:          template.ID,
			Name:        template.Name,
			Description: template.Description,
			Title:       template.Title,
			Content:     template.Content,
			Priority:    template.Priority,
			Type:        template.Type,
			IsDefault:   template.IsDefault,
			IsActive:    template.IsActive,
			SortOrder:   template.SortOrder,
			CreatedBy:   template.CreatedBy,
			CreatedAt:   template.CreatedAt,
			UpdatedAt:   template.UpdatedAt,
			Creator:     template.Creator,
		})
	}

	return responses, nil
}

// GetTaskTemplateByID 根据ID获取任务模板
func (s *TaskTemplateService) GetTaskTemplateByID(id string) (*models.TaskTemplateResponse, error) {
	var template models.TaskTemplate
	err := s.db.Preload("Creator").First(&template, "id = ? AND is_active = ?", id, true).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("模板不存在")
		}
		return nil, err
	}

	response := &models.TaskTemplateResponse{
		ID:          template.ID,
		Name:        template.Name,
		Description: template.Description,
		Title:       template.Title,
		Content:     template.Content,
		Priority:    template.Priority,
		Type:        template.Type,
		IsDefault:   template.IsDefault,
		IsActive:    template.IsActive,
		SortOrder:   template.SortOrder,
		CreatedBy:   template.CreatedBy,
		CreatedAt:   template.CreatedAt,
		UpdatedAt:   template.UpdatedAt,
		Creator:     template.Creator,
	}

	return response, nil
}

// CreateTaskTemplate 创建新任务模板
func (s *TaskTemplateService) CreateTaskTemplate(req models.TaskTemplateCreateRequest, createdBy string) (*models.TaskTemplateResponse, error) {
	// 检查名称是否已存在
	var count int64
	s.db.Model(&models.TaskTemplate{}).Where("name = ? AND is_active = ?", req.Name, true).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("模板名称已存在")
	}

	template := models.TaskTemplate{
		Name:        req.Name,
		Description: req.Description,
		Title:       req.Title,
		Content:     req.Content,
		Priority:    req.Priority,
		Type:        req.Type,
		IsDefault:   false, // 用户创建的模板不是默认模板
		IsActive:    true,
		SortOrder:   req.SortOrder,
		CreatedBy:   &createdBy,
	}

	// 如果没有指定排序顺序，设置为最大值+1
	if template.SortOrder == 0 {
		var maxOrder int
		s.db.Model(&models.TaskTemplate{}).Select("COALESCE(MAX(sort_order), 0)").Scan(&maxOrder)
		template.SortOrder = maxOrder + 1
	}

	// 设置默认优先级
	if template.Priority == "" {
		template.Priority = models.PriorityMedium
	}

	// 设置默认任务类型
	if template.Type == "" {
		template.Type = models.TypeTask
	}

	err := s.db.Create(&template).Error
	if err != nil {
		return nil, err
	}

	// 重新查询以获取关联数据
	return s.GetTaskTemplateByID(template.ID)
}

// UpdateTaskTemplate 更新任务模板
func (s *TaskTemplateService) UpdateTaskTemplate(id string, req models.TaskTemplateUpdateRequest) (*models.TaskTemplateResponse, error) {
	var template models.TaskTemplate
	err := s.db.First(&template, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("模板不存在")
		}
		return nil, err
	}

	// 检查名称是否与其他模板冲突
	var count int64
	s.db.Model(&models.TaskTemplate{}).Where("name = ? AND id != ? AND is_active = ?", req.Name, id, true).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("模板名称已存在")
	}

	// 更新字段
	template.Name = req.Name
	template.Description = req.Description
	template.Title = req.Title
	template.Content = req.Content
	template.Priority = req.Priority
	template.Type = req.Type
	template.SortOrder = req.SortOrder

	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}

	err = s.db.Save(&template).Error
	if err != nil {
		return nil, err
	}

	// 重新查询以获取关联数据
	return s.GetTaskTemplateByID(template.ID)
}

// DeleteTaskTemplate 删除任务模板（软删除）
func (s *TaskTemplateService) DeleteTaskTemplate(id string) error {
	var template models.TaskTemplate
	err := s.db.First(&template, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("模板不存在")
		}
		return err
	}

	// 系统默认模板不能删除
	if template.IsDefault {
		return errors.New("系统默认模板不能删除")
	}

	// 软删除
	err = s.db.Delete(&template).Error
	if err != nil {
		return err
	}

	return nil
}

// GetTaskTemplateStats 获取模板统计信息
func (s *TaskTemplateService) GetTaskTemplateStats() (map[string]interface{}, error) {
	var totalCount int64
	var activeCount int64
	var defaultCount int64

	// 总数
	s.db.Model(&models.TaskTemplate{}).Count(&totalCount)

	// 活跃数
	s.db.Model(&models.TaskTemplate{}).Where("is_active = ?", true).Count(&activeCount)

	// 默认模板数
	s.db.Model(&models.TaskTemplate{}).Where("is_default = ? AND is_active = ?", true, true).Count(&defaultCount)

	stats := map[string]interface{}{
		"total_count":   totalCount,
		"active_count":  activeCount,
		"default_count": defaultCount,
		"custom_count":  activeCount - defaultCount,
	}

	return stats, nil
}
