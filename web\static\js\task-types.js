// 任务类型管理JavaScript功能

// ==================== 全局函数定义（确保在页面加载时就可用） ====================



// 快速选择图标函数
window.quickSelectIcon = function(icon) {
    console.log('🎯 快速选择图标:', icon);

    const iconInput = document.getElementById('typeIcon');
    const iconPreview = document.getElementById('iconPreview');

    if (iconInput) {
        iconInput.value = icon;
        console.log('✅ 图标输入框已更新:', icon);
    }

    if (iconPreview) {
        iconPreview.textContent = icon;
        console.log('✅ 图标预览已更新:', icon);
    }

    // 关闭选择器
    const picker = document.getElementById('simpleIconPicker');
    const overlay = document.getElementById('pickerOverlay');
    if (picker) picker.remove();
    if (overlay) overlay.remove();

    console.log('✅ 图标选择完成，选择器已关闭');
};

// 全局图标选择器触发函数
window.openIconSelector = function() {
    console.log('🎯 === openIconSelector 被调用 ===');
    createSimpleIconPicker();
};

// 强制创建图标选择器的函数
window.forceCreateIconPicker = function() {
    console.log('🔧 === 强制创建图标选择器 ===');
    createSimpleIconPicker();
};

// 简单的测试函数
window.testIconSelector = function() {
    console.log('🧪 === 测试图标选择器 ===');
    console.log('openIconSelector 函数存在:', typeof window.openIconSelector);
    console.log('forceCreateIconPicker 函数存在:', typeof window.forceCreateIconPicker);
    console.log('createSimpleIconPicker 函数存在:', typeof createSimpleIconPicker);

    // 直接调用
    window.openIconSelector();
};

// 页面加载完成后立即测试
console.log('🔍 task-types.js 文件已加载');
console.log('🔍 openIconSelector 函数状态:', typeof window.openIconSelector);

// ==================== 原有代码开始 ====================

let currentEditingType = null;
let allTaskTypes = [];
let selectedTypeId = null;
let currentFilter = 'all';
let searchQuery = '';

// 图标分类数据
const iconCategories = {
    work: {
        name: '工作',
        icons: ['📋', '📝', '📊', '📈', '📉', '📅', '📆', '⏰', '⏱️', '⏲️', '🎯', '💼', '📞', '📧', '📬', '📭', '📮', '🗂️', '📂', '📁', '🗃️', '🗄️', '📑', '📄', '📃']
    },
    dev: {
        name: '开发',
        icons: ['🚀', '⚡', '🔧', '🔨', '⚙️', '🛠️', '🔩', '⚗️', '🧪', '🔬', '💡', '🔍', '🔎', '🐛', '🐞', '🔒', '🔓', '🔑', '💾', '💿', '🖥️', '💻', '📱', '⌨️', '🖱️']
    },
    ui: {
        name: '界面',
        icons: ['🎨', '🖌️', '🖍️', '✏️', '📐', '📏', '📌', '📍', '🔖', '🏷️', '📎', '🖇️', '📚', '📖', '📗', '📘', '📙', '📕', '🎭', '🎪', '🎬', '🎤', '🎧', '🎵', '🎶']
    },
    misc: {
        name: '其他',
        icons: ['⭐', '🌟', '✨', '💫', '🔥', '💥', '💢', '💨', '🌈', '☀️', '🌙', '❄️', '🌊', '🎉', '🎊', '🎈', '🎁', '🎀', '🏁', '🚩', '🎌', '🏆', '🏅', '🥇', '🥈']
    }
};

let currentIconCategory = 'work';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTaskTypeManagement();
    // 初始化时加载任务类型，用于更新任务表单选项
    loadTaskTypesForForm();

    // 调试：检查图标选择器相关元素
    setTimeout(() => {
        console.log('检查图标选择器元素:');
        console.log('iconPicker:', document.getElementById('iconPicker'));
        console.log('iconPreview:', document.getElementById('iconPreview'));
        console.log('typeIcon:', document.getElementById('typeIcon'));
    }, 1000);
});

// 初始化任务类型管理功能
function initializeTaskTypeManagement() {
    const taskTypeModal = document.getElementById('taskTypeModal');
    const closeTaskTypeModal = document.getElementById('closeTaskTypeModal');
    const closeTaskTypeModalHeader = document.getElementById('closeTaskTypeModalHeader');
    const addNewTypeBtn = document.getElementById('addNewTypeBtn');
    const cancelTypeEdit = document.getElementById('cancelTypeEdit');
    const taskTypeForm = document.getElementById('taskTypeForm');
    const typeIcon = document.getElementById('typeIcon');
    const iconPreview = document.getElementById('iconPreview');
    const typeColor = document.getElementById('typeColor');
    const typeColorText = document.getElementById('typeColorText');

    // 关闭模态框
    if (closeTaskTypeModal) {
        closeTaskTypeModal.addEventListener('click', function() {
            closeTaskTypeManager();
        });
    }

    // 头部关闭按钮
    if (closeTaskTypeModalHeader) {
        closeTaskTypeModalHeader.addEventListener('click', function() {
            closeTaskTypeManager();
        });
    }

    // 新建类型按钮
    if (addNewTypeBtn) {
        addNewTypeBtn.addEventListener('click', function() {
            showTypeEditForm();
        });
    }

    // 取消编辑按钮
    if (cancelTypeEdit) {
        cancelTypeEdit.addEventListener('click', function() {
            hideTypeEditForm();
        });
    }

    // 表单提交
    if (taskTypeForm) {
        taskTypeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveTaskType();
        });
    }

    // 图标预览
    if (typeIcon && iconPreview) {
        typeIcon.addEventListener('input', function() {
            iconPreview.textContent = this.value || '🚀';
        });
    }

    // 颜色选择器同步
    if (typeColor && typeColorText) {
        typeColor.addEventListener('change', function() {
            typeColorText.value = this.value;
        });
        
        typeColorText.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                typeColor.value = this.value;
            }
        });
    }

    // 搜索功能
    const searchInput = document.getElementById('typeSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            searchQuery = this.value.toLowerCase();
            renderTaskTypesList();
        });
    }

    // 筛选功能
    const filterButtons = ['filterAll', 'filterSystem', 'filterCustom'];
    filterButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', function() {
                // 更新筛选状态
                filterButtons.forEach(id => {
                    const btn = document.getElementById(id);
                    if (btn) {
                        btn.classList.remove('bg-blue-100', 'text-blue-700');
                        btn.classList.add('bg-gray-100', 'text-gray-600');
                    }
                });

                this.classList.remove('bg-gray-100', 'text-gray-600');
                this.classList.add('bg-blue-100', 'text-blue-700');

                // 设置筛选条件
                switch(buttonId) {
                    case 'filterAll':
                        currentFilter = 'all';
                        break;
                    case 'filterSystem':
                        currentFilter = 'system';
                        break;
                    case 'filterCustom':
                        currentFilter = 'custom';
                        break;
                }

                renderTaskTypesList();
            });
        }
    });

    // 点击模态框外部关闭
    if (taskTypeModal) {
        taskTypeModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeTaskTypeManager();
            }
        });
    }

    // 使用事件委托处理图标选择器
    document.addEventListener('click', function(e) {
        // 处理图标选择器切换
        const toggleButton = e.target.closest('[data-action="toggle-icon-picker"]');
        if (toggleButton) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 图标选择器按钮被点击');

            // 直接创建简单图标选择器
            createSimpleIconPicker();
            return;
        }

        // 点击外部关闭图标选择器
        const iconPicker = document.getElementById('iconPicker');
        if (iconPicker && !iconPicker.contains(e.target) &&
            !e.target.closest('[data-action="toggle-icon-picker"]')) {
            iconPicker.classList.add('hidden');
        }
    });
}

// 打开任务类型管理器
function openTaskTypeManager() {
    const modal = document.getElementById('taskTypeModal');
    const modalContent = document.getElementById('taskTypeModalContent');
    if (modal && modalContent) {
        modal.classList.remove('hidden');
        // 触发动画
        setTimeout(() => {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }, 10);
        loadTaskTypes();
        hideTypeEditForm();
    }
}

// 关闭任务类型管理器
function closeTaskTypeManager() {
    const modal = document.getElementById('taskTypeModal');
    const modalContent = document.getElementById('taskTypeModalContent');
    if (modal && modalContent) {
        // 退出动画
        modalContent.style.transform = 'scale(0.95)';
        modalContent.style.opacity = '0';
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
        hideTypeEditForm();
        currentEditingType = null;
    }
}

// 加载任务类型列表
function loadTaskTypes() {
    fetch('/api/task-types')
        .then(response => response.json())
        .then(data => {
            if (data.task_types) {
                allTaskTypes = data.task_types;
                renderTaskTypesList();
            }
        })
        .catch(error => {
            console.error('加载任务类型失败:', error);
            safeShowToast('加载任务类型失败', 'error');
        });
}

// 渲染任务类型列表
function renderTaskTypesList() {
    const container = document.getElementById('taskTypesList');
    const loadingElement = document.getElementById('typesLoading');
    const countElement = document.getElementById('typeCount');

    if (!container) return;

    // 隐藏加载状态
    if (loadingElement) {
        loadingElement.classList.add('hidden');
    }

    // 筛选和搜索
    let filteredTypes = allTaskTypes.filter(type => {
        // 筛选条件
        let matchesFilter = true;
        if (currentFilter === 'system') {
            matchesFilter = type.is_system;
        } else if (currentFilter === 'custom') {
            matchesFilter = !type.is_system;
        }

        // 搜索条件
        let matchesSearch = true;
        if (searchQuery) {
            matchesSearch = type.name.toLowerCase().includes(searchQuery) ||
                           type.display_name.toLowerCase().includes(searchQuery) ||
                           (type.description && type.description.toLowerCase().includes(searchQuery));
        }

        return matchesFilter && matchesSearch;
    });

    // 更新计数
    if (countElement) {
        countElement.textContent = filteredTypes.length;
    }

    if (allTaskTypes.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-20">
                <div class="relative mb-8">
                    <!-- 主图标 -->
                    <div class="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <!-- 装饰元素 -->
                    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                        <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                    </div>
                    <div class="absolute top-4 right-1/2 transform translate-x-8">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                    </div>
                    <div class="absolute top-4 left-1/2 transform -translate-x-8">
                        <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                    </div>
                </div>

                <h3 class="text-2xl font-bold text-gray-900 mb-3">开始创建任务类型</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                    任务类型帮助您更好地组织和分类项目工作，让团队协作更加高效有序
                </p>

                <div class="space-y-4">
                    <button onclick="document.getElementById('addNewTypeBtn').click()"
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-300 font-semibold inline-flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        创建第一个类型
                    </button>

                    <div class="text-sm text-gray-500">
                        <span class="inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            建议创建：功能开发、缺陷修复、优化改进等类型
                        </span>
                    </div>
                </div>
            </div>
        `;
        return;
    }

    if (filteredTypes.length === 0 && (searchQuery || currentFilter !== 'all')) {
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-medium text-gray-900 mb-1">未找到匹配的类型</h3>
                <p class="text-xs text-gray-500 mb-4">尝试调整搜索条件或筛选选项</p>
                <button onclick="document.getElementById('typeSearchInput').value=''; searchQuery=''; currentFilter='all'; document.getElementById('filterAll').click();"
                        class="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm">
                    清除筛选
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = filteredTypes.map(type => `
        <div class="group bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 p-4 cursor-pointer ${selectedTypeId === type.id ? 'border-blue-500 bg-blue-50' : ''}" onclick="selectTaskType('${type.id}')">
            <!-- 顶部装饰条 -->
            <div class="h-1 rounded-full mb-3" style="background-color: ${type.color}"></div>

            <!-- 图标和标题 -->
            <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center text-lg border" style="background-color: ${type.color}20; border-color: ${type.color}40;">
                    ${type.icon}
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-gray-900 truncate">${type.display_name}</h3>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-xs text-gray-500 font-mono">${type.name}</span>
                        ${type.is_system ?
                            `<span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700">系统</span>` :
                            `<span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-700">自定义</span>`
                        }
                    </div>
                </div>
            </div>

            <!-- 描述 -->
            ${type.description ? `<p class="text-sm text-gray-600 mb-3 line-clamp-2">${type.description}</p>` : ''}

            <!-- 操作按钮 -->
            <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full" style="background-color: ${type.color}"></div>
                    <span class="text-xs text-gray-400">${type.color}</span>
                </div>
                <div class="flex space-x-2">
                    <button onclick="event.stopPropagation(); editTaskType('${type.id}')"
                            class="px-2 py-1 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded text-xs transition-colors">
                        编辑
                    </button>
                    ${!type.is_system ? `
                        <button onclick="event.stopPropagation(); deleteTaskType('${type.id}')"
                                class="px-2 py-1 bg-red-50 hover:bg-red-100 text-red-600 rounded text-xs transition-colors">
                            删除
                        </button>
                    ` : `
                        <span class="px-2 py-1 bg-gray-100 text-gray-400 rounded text-xs">受保护</span>
                    `}
                </div>
            </div>
        </div>
    `).join('');
}

// 选择任务类型
function selectTaskType(typeId) {
    selectedTypeId = typeId;
    const type = allTaskTypes.find(t => t.id === typeId);
    if (type) {
        // 更新选中状态
        updateSelectedTypeUI(typeId);
        // 显示编辑表单
        showTypeEditForm(type);
    }
}

// 更新选中状态的UI
function updateSelectedTypeUI(typeId) {
    // 移除所有选中状态
    document.querySelectorAll('#taskTypesList .group').forEach(card => {
        card.classList.remove('border-blue-500', 'bg-blue-50');
        card.classList.add('border-gray-200');
    });

    // 添加选中状态
    const selectedCard = document.querySelector(`#taskTypesList .group[onclick="selectTaskType('${typeId}')"]`);
    if (selectedCard) {
        selectedCard.classList.remove('border-gray-200');
        selectedCard.classList.add('border-blue-500', 'bg-blue-50');
    }
}

// 显示类型编辑表单
function showTypeEditForm(type = null) {
    const form = document.getElementById('typeEditForm');
    const placeholder = document.getElementById('typeEditPlaceholder');
    const title = document.getElementById('typeFormTitle');

    // 隐藏占位符，显示表单
    if (placeholder) {
        placeholder.classList.add('hidden');
    }
    if (form) {
        form.classList.remove('hidden');
    }

    if (type) {
        // 编辑模式
        currentEditingType = type;
        selectedTypeId = type.id;
        if (title) title.textContent = '编辑类型';

        document.getElementById('editingTypeId').value = type.id;
        document.getElementById('typeName').value = type.name;
        document.getElementById('typeDisplayName').value = type.display_name;
        document.getElementById('typeIcon').value = type.icon;
        document.getElementById('typeColor').value = type.color;
        document.getElementById('typeColorText').value = type.color;
        document.getElementById('typeDescription').value = type.description || '';
        document.getElementById('iconPreview').textContent = type.icon;

        // 系统类型不能修改名称
        document.getElementById('typeName').disabled = type.is_system;

        // 更新选中状态
        updateSelectedTypeUI(type.id);
    } else {
        // 新建模式
        currentEditingType = null;
        selectedTypeId = null;
        if (title) title.textContent = '新建类型';

        document.getElementById('taskTypeForm').reset();
        document.getElementById('editingTypeId').value = '';
        document.getElementById('typeColor').value = '#3B82F6';
        document.getElementById('typeColorText').value = '#3B82F6';
        document.getElementById('iconPreview').textContent = '🚀';
        document.getElementById('typeName').disabled = false;

        // 清除选中状态
        document.querySelectorAll('#taskTypesList .group').forEach(card => {
            card.classList.remove('border-blue-500', 'bg-blue-50');
            card.classList.add('border-gray-200');
        });
    }
}

// 隐藏类型编辑表单
function hideTypeEditForm() {
    const form = document.getElementById('typeEditForm');
    const placeholder = document.getElementById('typeEditPlaceholder');

    // 隐藏表单，显示占位符
    if (form) {
        form.classList.add('hidden');
    }
    if (placeholder) {
        placeholder.classList.remove('hidden');
    }

    currentEditingType = null;
    selectedTypeId = null;

    // 清除选中状态
    document.querySelectorAll('#taskTypesList .group').forEach(card => {
        card.classList.remove('border-blue-500', 'bg-blue-50');
        card.classList.add('border-gray-200');
    });
}

// 图标选择器功能 - 简化版本
function toggleIconPicker() {
    console.log('🎯 === toggleIconPicker 被调用 ===');

    // 直接创建简单图标选择器，不依赖复杂的原始选择器
    createSimpleIconPicker();
}

// 创建简单的图标选择器
function createSimpleIconPicker() {
    console.log('🎨 === 创建简单图标选择器 ===');

    // 移除已存在的选择器
    const existingPicker = document.getElementById('simpleIconPicker');
    if (existingPicker) {
        console.log('移除已存在的选择器');
        existingPicker.remove();
    }

    const iconInput = document.getElementById('typeIcon');
    const iconPreview = document.getElementById('iconPreview');

    console.log('iconInput 存在:', !!iconInput);
    console.log('iconPreview 存在:', !!iconPreview);

    if (!iconInput) {
        console.error('❌ 找不到图标输入框，无法创建选择器');
        alert('找不到图标输入框，请刷新页面重试');
        return;
    }

    // 创建选择器
    const picker = document.createElement('div');
    picker.id = 'simpleIconPicker';
    picker.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        padding: 20px;
        z-index: 10000;
        max-width: 400px;
        width: 90vw;
    `;

    const commonIcons = [
        '📋', '🚀', '🐛', '⚡', '🔧', '🎯',
        '📊', '📝', '💡', '🔍', '⚙️', '🛠️',
        '🎨', '📅', '💼', '📞', '📧', '🔒',
        '🔑', '💾', '🌟', '🎉', '🔥', '💎'
    ];

    picker.innerHTML = `
        <div style="margin-bottom: 16px;">
            <h3 style="font-size: 16px; font-weight: 600; color: #374151; margin-bottom: 12px;">选择图标</h3>
            <div style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px;">
                ${commonIcons.map(icon => `
                    <button type="button"
                            onclick="window.quickSelectIcon('${icon}')"
                            style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 1px solid #d1d5db; border-radius: 8px; background: white; cursor: pointer; transition: all 0.2s;"
                            onmouseover="this.style.background='#eff6ff'; this.style.borderColor='#3b82f6';"
                            onmouseout="this.style.background='white'; this.style.borderColor='#d1d5db';"
                            title="选择 ${icon}">
                        ${icon}
                    </button>
                `).join('')}
            </div>
        </div>
        <div style="display: flex; gap: 8px;">
            <button type="button"
                    onclick="document.getElementById('simpleIconPicker').remove()"
                    style="flex: 1; padding: 8px 16px; background: #f3f4f6; color: #374151; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;"
                    onmouseover="this.style.background='#e5e7eb';"
                    onmouseout="this.style.background='#f3f4f6';">
                关闭
            </button>
            <button type="button"
                    onclick="window.quickSelectIcon('🚀')"
                    style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;"
                    onmouseover="this.style.background='#2563eb';"
                    onmouseout="this.style.background='#3b82f6';">
                默认 🚀
            </button>
        </div>
    `;

    // 添加到body
    document.body.appendChild(picker);
    console.log('✅ 图标选择器已创建并添加到DOM');

    // 添加背景遮罩
    const overlay = document.createElement('div');
    overlay.id = 'pickerOverlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
    `;
    overlay.onclick = function() {
        picker.remove();
        overlay.remove();
    };
    document.body.appendChild(overlay);
}



// 确保函数在全局作用域中可用
window.toggleIconPicker = toggleIconPicker;

// 测试函数
function testIconPicker() {
    console.log('🧪 === 测试图标选择器功能 ===');
    const form = document.getElementById('typeEditForm');
    const iconInput = document.getElementById('typeIcon');

    console.log('typeEditForm element:', !!form);
    console.log('typeIcon element:', !!iconInput);
    console.log('表单是否隐藏:', form ? form.classList.contains('hidden') : 'N/A');

    // 直接创建简单图标选择器
    createSimpleIconPicker();
}

// 强制创建图标选择器的测试函数
function forceCreateIconPicker() {
    console.log('🔧 === 强制创建图标选择器 ===');
    createSimpleIconPicker();
}

// 测试图标选择功能
function testSelectIcon() {
    console.log('🎯 === 测试图标选择功能 ===');
    if (window.quickSelectIcon) {
        window.quickSelectIcon('🎯');
    } else {
        console.error('quickSelectIcon 函数不存在');
    }
}

window.testIconPicker = testIconPicker;
window.forceCreateIconPicker = forceCreateIconPicker;
window.testSelectIcon = testSelectIcon;

// 添加直接的按钮点击测试
window.testButtonClick = function() {
    console.log('🧪 === 测试按钮点击 ===');
    const buttons = document.querySelectorAll('[data-action="toggle-icon-picker"]');
    console.log('找到的按钮数量:', buttons.length);

    buttons.forEach((btn, index) => {
        console.log(`按钮 ${index}:`, btn.tagName, btn.textContent.trim());
        console.log(`按钮 ${index} 可见性:`, !btn.closest('.hidden'));
    });

    // 直接触发点击事件
    if (buttons.length > 0) {
        console.log('模拟点击第一个按钮');
        buttons[0].click();
    }
};

function showIconCategory(category) {
    console.log('showIconCategory 被调用:', category);
    currentIconCategory = category;

    // 更新分类按钮状态
    document.querySelectorAll('.icon-category-btn').forEach(btn => {
        btn.classList.remove('bg-blue-100', 'text-blue-700');
        btn.classList.add('bg-gray-100', 'text-gray-600');
    });

    const activeBtn = document.querySelector(`[data-category="${category}"]`);
    if (activeBtn) {
        activeBtn.classList.remove('bg-gray-100', 'text-gray-600');
        activeBtn.classList.add('bg-blue-100', 'text-blue-700');
    }

    renderIconGrid();
}

// 确保函数在全局作用域中可用
window.showIconCategory = showIconCategory;

function renderIconGrid() {
    const grid = document.getElementById('iconGrid');
    if (!grid || !iconCategories[currentIconCategory]) return;

    const icons = iconCategories[currentIconCategory].icons;
    grid.innerHTML = icons.map(icon => `
        <button type="button"
                onclick="selectIcon('${icon}')"
                class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 hover:border-blue-300 border border-gray-200 rounded transition-colors"
                title="${icon}">
            ${icon}
        </button>
    `).join('');
}

function selectIcon(icon) {
    console.log('=== selectIcon 被调用 ===', icon);
    const iconInput = document.getElementById('typeIcon');
    const iconPreview = document.getElementById('iconPreview');
    const iconPicker = document.getElementById('iconPicker');
    const searchInput = document.getElementById('iconSearchInput');

    console.log('iconInput 存在:', !!iconInput);
    console.log('iconPreview 存在:', !!iconPreview);
    console.log('iconPicker 存在:', !!iconPicker);

    if (iconInput) {
        iconInput.value = icon;
        console.log('已设置图标输入框值:', icon);

        // 触发input事件以更新预览
        iconInput.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        console.error('找不到图标输入框');
    }

    if (iconPreview) {
        iconPreview.textContent = icon;
        console.log('已更新图标预览:', icon);
    } else {
        console.error('找不到图标预览元素');
    }

    if (iconPicker) {
        iconPicker.classList.add('hidden');
        console.log('已隐藏图标选择器');
    }

    if (searchInput) {
        searchInput.value = '';
    }

    console.log('图标选择完成');
}

function searchIcons(query) {
    console.log('searchIcons 被调用:', query);
    if (!query.trim()) {
        renderIconGrid();
        return;
    }

    const grid = document.getElementById('iconGrid');
    if (!grid) return;

    // 搜索所有分类中的图标
    let allIcons = [];
    Object.values(iconCategories).forEach(category => {
        allIcons = allIcons.concat(category.icons);
    });

    // 去重
    allIcons = [...new Set(allIcons)];

    // 这里可以根据需要添加更智能的搜索逻辑
    // 目前简单显示所有图标让用户选择
    grid.innerHTML = allIcons.map(icon => `
        <button type="button"
                onclick="selectIcon('${icon}')"
                class="w-8 h-8 flex items-center justify-center text-lg hover:bg-blue-50 hover:border-blue-300 border border-gray-200 rounded transition-colors"
                title="${icon}">
            ${icon}
        </button>
    `).join('');
}

// 确保函数在全局作用域中可用
window.selectIcon = selectIcon;
window.searchIcons = searchIcons;



// 编辑任务类型
function editTaskType(typeId) {
    const type = allTaskTypes.find(t => t.id === typeId);
    if (type) {
        showTypeEditForm(type);
    }
}

// 保存任务类型
function saveTaskType() {
    const formData = new FormData(document.getElementById('taskTypeForm'));
    const typeData = {
        name: formData.get('name'),
        display_name: formData.get('display_name'),
        icon: formData.get('icon'),
        color: formData.get('color'),
        description: formData.get('description') || '',
        sort_order: 0
    };

    const typeId = document.getElementById('editingTypeId').value;
    const isEdit = typeId !== '';
    const url = isEdit ? `/api/task-types/${typeId}` : '/api/task-types';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(typeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            safeShowToast(data.error, 'error');
        } else {
            safeShowToast(isEdit ? '类型更新成功' : '类型创建成功', 'success');
            hideTypeEditForm();
            loadTaskTypes();
            // 更新任务表单中的类型选项
            updateTaskFormTypeOptions();
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        safeShowToast('保存失败，请稍后重试', 'error');
    });
}

// 删除任务类型
function deleteTaskType(typeId) {
    const type = allTaskTypes.find(t => t.id === typeId);
    if (!type) return;

    if (!confirm(`确定要删除类型"${type.display_name}"吗？\n注意：如果有任务正在使用此类型，将无法删除。`)) {
        return;
    }

    fetch(`/api/task-types/${typeId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            safeShowToast(data.error, 'error');
        } else {
            safeShowToast('类型删除成功', 'success');
            loadTaskTypes();
            // 更新任务表单中的类型选项
            updateTaskFormTypeOptions();
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        safeShowToast('删除失败，请稍后重试', 'error');
    });
}

// 加载任务类型用于表单初始化
function loadTaskTypesForForm() {
    fetch('/api/task-types')
        .then(response => response.json())
        .then(data => {
            if (data.task_types) {
                allTaskTypes = data.task_types;
                updateTaskFormTypeOptions();
            }
        })
        .catch(error => {
            console.error('加载任务类型失败:', error);
        });
}

// 更新任务表单中的类型选项
function updateTaskFormTypeOptions() {
    const taskTypeSelect = document.getElementById('taskType');
    if (!taskTypeSelect) return;

    // 保存当前选中的值
    const currentValue = taskTypeSelect.value;

    // 清空现有选项
    taskTypeSelect.innerHTML = '';

    // 添加新选项
    allTaskTypes.forEach(type => {
        if (type.is_active) {
            const option = document.createElement('option');
            option.value = type.name;
            option.textContent = `${type.icon} ${type.display_name}`;
            taskTypeSelect.appendChild(option);
        }
    });

    // 恢复选中的值（如果还存在）
    if (currentValue && Array.from(taskTypeSelect.options).some(opt => opt.value === currentValue)) {
        taskTypeSelect.value = currentValue;
    }
}

// 独立的 toast 实现，完全避免递归
function safeShowToast(message, type = 'info') {
    // 防止重复调用
    if (safeShowToast._isRunning) {
        console.log('safeShowToast 重复调用被阻止:', message);
        return;
    }
    safeShowToast._isRunning = true;

    try {
        // 创建简单的 toast 元素
        const toast = document.createElement('div');
        const toastId = 'safe-toast-' + Date.now();
        toast.id = toastId;

        // 设置样式
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 350px;
            word-wrap: break-word;
        `;

        toast.textContent = message;
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }
        }, 3000);

    } catch (error) {
        console.error('safeShowToast 执行错误:', error);
        // 最后的降级处理
        console.log(`Toast: ${message} (${type})`);
    } finally {
        // 重置标志
        setTimeout(() => {
            safeShowToast._isRunning = false;
        }, 100);
    }
}
