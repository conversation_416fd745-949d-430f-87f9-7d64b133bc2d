package services

import (
	"fmt"
	"projectm2/internal/models"

	"gorm.io/gorm"
)

// TaskTypeService 任务类型服务
type TaskTypeService struct {
	db *gorm.DB
}

// NewTaskTypeService 创建新的任务类型服务实例
func NewTaskTypeService(db *gorm.DB) *TaskTypeService {
	return &TaskTypeService{db: db}
}

// GetAllTaskTypes 获取所有任务类型
func (s *TaskTypeService) GetAllTaskTypes() ([]models.TaskTypeModel, error) {
	var taskTypes []models.TaskTypeModel
	err := s.db.Where("is_active = ?", true).Order("sort_order ASC, created_at ASC").Find(&taskTypes).Error
	return taskTypes, err
}

// GetTaskTypeByID 根据ID获取任务类型
func (s *TaskTypeService) GetTaskTypeByID(id string) (*models.TaskTypeModel, error) {
	var taskType models.TaskTypeModel
	err := s.db.First(&taskType, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &taskType, nil
}

// GetTaskTypeByName 根据名称获取任务类型
func (s *TaskTypeService) GetTaskTypeByName(name string) (*models.TaskTypeModel, error) {
	var taskType models.TaskTypeModel
	err := s.db.First(&taskType, "name = ? AND is_active = ?", name, true).Error
	if err != nil {
		return nil, err
	}
	return &taskType, nil
}

// CreateTaskType 创建新任务类型
func (s *TaskTypeService) CreateTaskType(req models.TaskTypeCreateRequest) (*models.TaskTypeModel, error) {
	// 检查名称是否已存在
	var count int64
	s.db.Model(&models.TaskTypeModel{}).Where("name = ?", req.Name).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("任务类型名称已存在")
	}

	taskType := models.TaskTypeModel{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Icon:        req.Icon,
		Color:       req.Color,
		Description: req.Description,
		IsSystem:    false, // 用户创建的类型不是系统类型
		IsActive:    true,
		SortOrder:   req.SortOrder,
	}

	// 如果没有指定排序顺序，设置为最大值+1
	if taskType.SortOrder == 0 {
		var maxOrder int
		s.db.Model(&models.TaskTypeModel{}).Select("COALESCE(MAX(sort_order), 0)").Scan(&maxOrder)
		taskType.SortOrder = maxOrder + 1
	}

	err := s.db.Create(&taskType).Error
	if err != nil {
		return nil, err
	}

	return &taskType, nil
}

// UpdateTaskType 更新任务类型
func (s *TaskTypeService) UpdateTaskType(id string, req models.TaskTypeUpdateRequest) (*models.TaskTypeModel, error) {
	taskType, err := s.GetTaskTypeByID(id)
	if err != nil {
		return nil, err
	}

	// 系统类型只能更新部分字段
	if taskType.IsSystem {
		// 系统类型只允许更新显示名称、图标、颜色和描述
		if req.DisplayName != "" {
			taskType.DisplayName = req.DisplayName
		}
		if req.Icon != "" {
			taskType.Icon = req.Icon
		}
		if req.Color != "" {
			taskType.Color = req.Color
		}
		if req.Description != "" {
			taskType.Description = req.Description
		}
		if req.SortOrder != nil {
			taskType.SortOrder = *req.SortOrder
		}
	} else {
		// 非系统类型可以更新所有字段
		if req.DisplayName != "" {
			taskType.DisplayName = req.DisplayName
		}
		if req.Icon != "" {
			taskType.Icon = req.Icon
		}
		if req.Color != "" {
			taskType.Color = req.Color
		}
		if req.Description != "" {
			taskType.Description = req.Description
		}
		if req.IsActive != nil {
			taskType.IsActive = *req.IsActive
		}
		if req.SortOrder != nil {
			taskType.SortOrder = *req.SortOrder
		}
	}

	err = s.db.Save(taskType).Error
	if err != nil {
		return nil, err
	}

	return taskType, nil
}

// DeleteTaskType 删除任务类型
func (s *TaskTypeService) DeleteTaskType(id string) error {
	taskType, err := s.GetTaskTypeByID(id)
	if err != nil {
		return err
	}

	// 系统类型不能删除
	if taskType.IsSystem {
		return fmt.Errorf("系统预设类型不能删除")
	}

	// 检查是否有任务使用此类型
	var taskCount int64
	s.db.Model(&models.Task{}).Where("type = ?", taskType.Name).Count(&taskCount)
	if taskCount > 0 {
		return fmt.Errorf("该类型正在被 %d 个任务使用，无法删除", taskCount)
	}

	// 软删除
	return s.db.Delete(taskType).Error
}

// InitializeDefaultTaskTypes 初始化默认任务类型
func (s *TaskTypeService) InitializeDefaultTaskTypes() error {
	// 检查是否已经初始化过
	var count int64
	s.db.Model(&models.TaskTypeModel{}).Count(&count)
	if count > 0 {
		return nil // 已经有数据，跳过初始化
	}

	// 插入默认类型
	defaultTypes := models.GetDefaultTaskTypes()
	for _, taskType := range defaultTypes {
		if err := s.db.Create(&taskType).Error; err != nil {
			return fmt.Errorf("初始化默认任务类型失败: %v", err)
		}
	}

	return nil
}

// GetTaskTypeStats 获取任务类型统计信息
func (s *TaskTypeService) GetTaskTypeStats() (map[string]int, error) {
	var results []struct {
		Type  string `json:"type"`
		Count int    `json:"count"`
	}

	err := s.db.Model(&models.Task{}).
		Select("type, COUNT(*) as count").
		Group("type").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.Type] = result.Count
	}

	return stats, nil
}
