package services

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"projectm2/internal/models"
	"sync"
	"time"
)

// Session 会话结构
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	User      *models.User `json:"user"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

// SessionService 会话服务
type SessionService struct {
	sessions map[string]*Session
	mutex    sync.RWMutex
	duration time.Duration
}

// NewSessionService 创建新的会话服务实例
func NewSessionService() *SessionService {
	return &SessionService{
		sessions: make(map[string]*Session),
		duration: 24 * time.Hour, // 默认24小时过期
	}
}

// generateSessionID 生成会话ID
func (s *SessionService) generateSessionID() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateSession 创建会话
func (s *SessionService) CreateSession(user *models.User) (*Session, error) {
	sessionID, err := s.generateSessionID()
	if err != nil {
		return nil, fmt.Errorf("生成会话ID失败: %v", err)
	}

	session := &Session{
		ID:        sessionID,
		UserID:    user.ID,
		User:      user,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(s.duration),
	}

	s.mutex.Lock()
	s.sessions[sessionID] = session
	s.mutex.Unlock()

	return session, nil
}

// GetSession 获取会话
func (s *SessionService) GetSession(sessionID string) (*Session, error) {
	s.mutex.RLock()
	session, exists := s.sessions[sessionID]
	s.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("会话不存在")
	}

	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		s.DeleteSession(sessionID)
		return nil, fmt.Errorf("会话已过期")
	}

	return session, nil
}

// DeleteSession 删除会话
func (s *SessionService) DeleteSession(sessionID string) {
	s.mutex.Lock()
	delete(s.sessions, sessionID)
	s.mutex.Unlock()
}

// RefreshSession 刷新会话
func (s *SessionService) RefreshSession(sessionID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	session, exists := s.sessions[sessionID]
	if !exists {
		return fmt.Errorf("会话不存在")
	}

	session.ExpiresAt = time.Now().Add(s.duration)
	return nil
}

// CleanupExpiredSessions 清理过期会话
func (s *SessionService) CleanupExpiredSessions() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	for sessionID, session := range s.sessions {
		if now.After(session.ExpiresAt) {
			delete(s.sessions, sessionID)
		}
	}
}

// GetActiveSessions 获取活跃会话数量
func (s *SessionService) GetActiveSessions() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	return len(s.sessions)
}

// StartCleanupRoutine 启动清理例程
func (s *SessionService) StartCleanupRoutine() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
		defer ticker.Stop()
		
		for range ticker.C {
			s.CleanupExpiredSessions()
		}
	}()
}
