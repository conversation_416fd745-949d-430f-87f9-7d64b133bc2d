package handlers

import (
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
)

// TaskVersionHandler 任务版本处理器
type TaskVersionHandler struct {
	taskVersionService *services.TaskVersionService
}

// NewTaskVersionHandler 创建新的任务版本处理器
func NewTaskVersionHandler(taskVersionService *services.TaskVersionService) *TaskVersionHandler {
	return &TaskVersionHandler{
		taskVersionService: taskVersionService,
	}
}

// GetTaskVersions 获取所有任务版本
func (h *TaskVersionHandler) GetTaskVersions(c *gin.Context) {
	versions, err := h.taskVersionService.GetAllTaskVersions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取版本列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
		"success":       true,
		"task_versions": versions,
	})
}

// GetTaskVersionByID 根据ID获取任务版本
func (h *TaskVersionHandler) GetTaskVersionByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "版本ID不能为空",
		})
		return
	}

	version, err := h.taskVersionService.GetTaskVersionByID(id)
	if err != nil {
		if err.Error() == "版本不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取版本信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"task_version": version,
	})
}

// CreateTaskVersion 创建任务版本
func (h *TaskVersionHandler) CreateTaskVersion(c *gin.Context) {
	var req models.TaskVersionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	version, err := h.taskVersionService.CreateTaskVersion(req)
	if err != nil {
		if err.Error() == "版本名称已存在" {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建版本失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success":      true,
		"message":      "版本创建成功",
		"task_version": version,
	})
}

// UpdateTaskVersion 更新任务版本
func (h *TaskVersionHandler) UpdateTaskVersion(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "版本ID不能为空",
		})
		return
	}

	var req models.TaskVersionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	version, err := h.taskVersionService.UpdateTaskVersion(id, req)
	if err != nil {
		if err.Error() == "版本不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		if err.Error() == "版本名称已存在" {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新版本失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "版本更新成功",
		"task_version": version,
	})
}

// DeleteTaskVersion 删除任务版本
func (h *TaskVersionHandler) DeleteTaskVersion(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "版本ID不能为空",
		})
		return
	}

	err := h.taskVersionService.DeleteTaskVersion(id)
	if err != nil {
		if err.Error() == "版本不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		if err.Error() == "该版本下还有任务，无法删除" {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除版本失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "版本删除成功",
	})
}

// GetActiveTaskVersions 获取激活的任务版本（用于下拉选择）
func (h *TaskVersionHandler) GetActiveTaskVersions(c *gin.Context) {
	versions, err := h.taskVersionService.GetActiveTaskVersions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取激活版本列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"task_versions": versions,
	})
}

// GetTaskVersionStats 获取版本统计信息
func (h *TaskVersionHandler) GetTaskVersionStats(c *gin.Context) {
	stats, err := h.taskVersionService.GetTaskVersionStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取版本统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"stats":   stats,
	})
}
