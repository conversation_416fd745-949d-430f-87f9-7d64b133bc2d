// 手动测试脚本 - 在浏览器控制台中运行

// 1. 首先打开编辑模态框（点击编辑按钮）
// 2. 然后在控制台运行以下代码：

function manualTestFields() {
    console.log('🧪 开始手动测试字段设置');
    
    // 获取元素
    const startDateElement = document.getElementById('taskStartDate');
    const endDateElement = document.getElementById('taskEndDate');
    const assigneeElement = document.getElementById('taskAssignee');
    
    console.log('🧪 元素检查:');
    console.log('  - 开始日期元素:', startDateElement);
    console.log('  - 结束日期元素:', endDateElement);
    console.log('  - 分配用户元素:', assigneeElement);
    
    // 手动设置值
    if (startDateElement) {
        startDateElement.value = '2025-07-07';
        console.log('🧪 手动设置开始日期: 2025-07-07');
        console.log('🧪 设置后的值:', startDateElement.value);
    }
    
    if (endDateElement) {
        endDateElement.value = '2025-07-10';
        console.log('🧪 手动设置结束日期: 2025-07-10');
        console.log('🧪 设置后的值:', endDateElement.value);
    }
    
    if (assigneeElement) {
        assigneeElement.value = 'fc358eea-3243-42aa-89bc-76863c833384';
        console.log('🧪 手动设置分配用户: fc358eea-3243-42aa-89bc-76863c833384');
        console.log('🧪 设置后的值:', assigneeElement.value);
        
        // 打印所有选项
        console.log('🧪 所有用户选项:');
        for (let i = 0; i < assigneeElement.options.length; i++) {
            console.log(`  ${i}: value="${assigneeElement.options[i].value}", text="${assigneeElement.options[i].text}"`);
        }
    }
    
    console.log('🧪 手动测试完成');
}

// 延迟测试 - 检查值是否被其他代码重置
function delayedCheck() {
    setTimeout(() => {
        console.log('🧪 延迟检查字段值:');
        const startDateElement = document.getElementById('taskStartDate');
        const endDateElement = document.getElementById('taskEndDate');
        const assigneeElement = document.getElementById('taskAssignee');
        
        if (startDateElement) {
            console.log('🧪 开始日期当前值:', startDateElement.value);
        }
        if (endDateElement) {
            console.log('🧪 结束日期当前值:', endDateElement.value);
        }
        if (assigneeElement) {
            console.log('🧪 分配用户当前值:', assigneeElement.value);
        }
    }, 2000);
}

console.log('🧪 手动测试脚本已加载');
console.log('🧪 使用方法:');
console.log('  1. 打开编辑任务模态框');
console.log('  2. 运行: manualTestFields()');
console.log('  3. 运行: delayedCheck() 检查值是否被重置');
