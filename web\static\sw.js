// Service Worker for ProjectM2
// 提供离线支持和缓存管理

const CACHE_NAME = 'projectm2-v1.0.0';
const STATIC_CACHE = 'projectm2-static-v1.0.0';
const DYNAMIC_CACHE = 'projectm2-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/static/css/style.css',
    '/static/js/app.js',
    '/static/images/logo.png',
    // 添加其他静态资源
];

// 需要缓存的API端点
const CACHE_API_PATTERNS = [
    /^\/api\/tasks/,
    /^\/api\/users/,
    /^\/api\/settings/
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('静态资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('静态资源缓存失败:', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

// 拦截网络请求
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== location.origin) {
        return;
    }
    
    // 静态资源使用缓存优先策略
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request));
        return;
    }
    
    // API请求使用网络优先策略
    if (isAPIRequest(request)) {
        event.respondWith(networkFirst(request));
        return;
    }
    
    // 其他请求使用默认策略
    event.respondWith(
        fetch(request).catch(() => {
            // 网络失败时返回离线页面
            if (request.destination === 'document') {
                return caches.match('/offline.html');
            }
        })
    );
});

// 缓存优先策略
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('缓存优先策略失败:', error);
        throw error;
    }
}

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok && request.method === 'GET') {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('网络请求失败，尝试缓存:', request.url);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

// 判断是否为静态资源
function isStaticAsset(request) {
    const url = new URL(request.url);
    return url.pathname.startsWith('/static/') || 
           url.pathname === '/' ||
           url.pathname.endsWith('.html') ||
           url.pathname.endsWith('.css') ||
           url.pathname.endsWith('.js') ||
           url.pathname.endsWith('.png') ||
           url.pathname.endsWith('.jpg') ||
           url.pathname.endsWith('.svg');
}

// 判断是否为API请求
function isAPIRequest(request) {
    const url = new URL(request.url);
    return CACHE_API_PATTERNS.some(pattern => pattern.test(url.pathname));
}

// 后台同步
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // 处理离线时的操作队列
        const offlineActions = await getOfflineActions();
        
        for (const action of offlineActions) {
            try {
                await fetch(action.url, action.options);
                await removeOfflineAction(action.id);
            } catch (error) {
                console.error('后台同步失败:', error);
            }
        }
    } catch (error) {
        console.error('后台同步处理失败:', error);
    }
}

// 推送通知
self.addEventListener('push', event => {
    if (!event.data) return;
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/static/images/icon-192.png',
        badge: '/static/images/badge-72.png',
        tag: data.tag || 'default',
        data: data.data || {},
        actions: data.actions || []
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    const { action, data } = event;
    
    if (action === 'open') {
        event.waitUntil(
            clients.openWindow(data.url || '/')
        );
    } else if (action === 'dismiss') {
        // 忽略通知
    } else {
        // 默认行为：打开应用
        event.waitUntil(
            clients.matchAll({ type: 'window' })
                .then(clientList => {
                    if (clientList.length > 0) {
                        return clientList[0].focus();
                    }
                    return clients.openWindow('/');
                })
        );
    }
});

// 辅助函数：获取离线操作
async function getOfflineActions() {
    // 从 IndexedDB 或其他存储中获取离线操作
    return [];
}

// 辅助函数：移除离线操作
async function removeOfflineAction(actionId) {
    // 从存储中移除已完成的离线操作
}

// 错误处理
self.addEventListener('error', event => {
    console.error('Service Worker 错误:', event.error);
});

self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker 未处理的 Promise 拒绝:', event.reason);
});

console.log('Service Worker 脚本加载完成');
