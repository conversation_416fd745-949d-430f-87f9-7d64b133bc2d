<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务模态框优化验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7fafc;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.2s;
        }
        
        .test-button:hover {
            background: #3182ce;
        }
        
        .success { color: #38a169; }
        .error { color: #e53e3e; }
        .info { color: #3182ce; }
        
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }
        
        .optimization-summary {
            background: #f0fff4;
            border: 2px solid #9ae6b4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .optimization-summary h3 {
            color: #38a169;
            margin-bottom: 15px;
        }
        
        .optimization-list {
            list-style: none;
            padding: 0;
        }
        
        .optimization-list li {
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .optimization-list li:last-child {
            border-bottom: none;
        }
        
        .check-mark {
            color: #38a169;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 任务模态框优化验证</h1>
            <p>验证布局优化效果和功能完整性</p>
        </div>

        <div class="optimization-summary">
            <h3>✅ 优化完成总结</h3>
            <ul class="optimization-list">
                <li><span class="check-mark">✓</span>模态框宽度扩大：sm:max-w-lg → sm:max-w-2xl (增加31%空间)</li>
                <li><span class="check-mark">✓</span>快速时间选择：3列布局 → 2列布局 (避免文字截断)</li>
                <li><span class="check-mark">✓</span>富文本编辑器：200px → 240px高度 (增加20%编辑空间)</li>
                <li><span class="check-mark">✓</span>表单间距优化：space-y-4 → space-y-5 (提升视觉层次)</li>
                <li><span class="check-mark">✓</span>内边距增加：提供更舒适的视觉空间</li>
                <li><span class="check-mark">✓</span>按钮样式优化：更好的悬停效果和视觉反馈</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="testAPI()">🔍 测试API连接</button>
            <button class="test-button" onclick="testTimePresets()">⏰ 测试时间预设</button>
            <a href="http://localhost:8089" target="_blank" class="test-button" style="text-decoration: none; display: inline-block;">🚀 打开主应用</a>
        </div>

        <div id="test-results">
            <p class="info">点击上方按钮开始验证...</p>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #ebf8ff; border-radius: 8px;">
            <h3 style="color: #3182ce; margin-bottom: 15px;">📋 手动验证步骤</h3>
            <ol style="line-height: 1.8;">
                <li><strong>打开主应用</strong> - 点击上方"打开主应用"按钮</li>
                <li><strong>点击新建任务</strong> - 观察模态框是否更宽，布局是否舒适</li>
                <li><strong>检查富文本编辑器</strong> - 工具栏是否完整，编辑区域是否足够</li>
                <li><strong>测试快速时间选择</strong> - 按钮是否为2列布局，文字是否完整显示</li>
                <li><strong>验证表单布局</strong> - 各字段间距是否合理，无内容遮盖</li>
                <li><strong>测试功能完整性</strong> - 所有功能是否正常工作</li>
            </ol>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.style.backgroundColor = type === 'success' ? '#f0fff4' : 
                                             type === 'error' ? '#fed7d7' : '#ebf8ff';
            resultItem.innerHTML = message;
            resultsDiv.appendChild(resultItem);
        }

        async function testAPI() {
            try {
                showResult('🔄 正在测试API连接...', 'info');
                const response = await fetch('http://localhost:8089/');
                if (response.ok) {
                    showResult('✅ API连接成功 - 服务器正常运行', 'success');
                } else {
                    showResult(`❌ API连接失败 - 状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ API连接失败: ${error.message}`, 'error');
            }
        }

        async function testTimePresets() {
            try {
                showResult('🔄 正在测试时间预设API...', 'info');
                const response = await fetch('http://localhost:8089/api/time/presets');
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ 时间预设API正常 - 预设数量: ${data.presets ? data.presets.length : 0}`, 'success');
                    if (data.presets && data.presets.length > 0) {
                        const presetNames = data.presets.map(p => p.name).join(', ');
                        showResult(`📋 可用预设: ${presetNames}`, 'info');
                    }
                } else {
                    showResult(`❌ 时间预设API失败 - 状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 时间预设API失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示优化完成信息
        window.addEventListener('load', function() {
            showResult('🎉 任务模态框布局优化已完成！请进行验证测试。', 'success');
        });
    </script>
</body>
</html>
