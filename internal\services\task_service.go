package services

import (
	"fmt"
	"projectm2/internal/models"
	"time"

	"gorm.io/gorm"
)

// convertCustomDateToTime 转换CustomDate到*time.Time
func convertCustomDateToTime(cd *models.CustomDate) *time.Time {
	if cd == nil || cd.Time.IsZero() {
		return nil
	}
	return &cd.Time
}

// TaskService 任务服务
type TaskService struct {
	db *gorm.DB
}

// NewTaskService 创建新的任务服务实例
func NewTaskService(db *gorm.DB) *TaskService {
	return &TaskService{db: db}
}

// GetAllTasks 获取所有任务，按状态和位置排序
func (s *TaskService) GetAllTasks() ([]models.Task, error) {
	var tasks []models.Task
	// 优化查询：只获取基本任务信息，避免N+1查询问题
	err := s.db.Order("status, position").Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	// 如果需要用户信息，可以单独批量查询
	// 这里暂时不加载用户信息以提高性能
	return tasks, nil
}

// GetAllTasksWithUsers 获取所有任务及关联用户信息（当需要用户信息时使用）
func (s *TaskService) GetAllTasksWithUsers() ([]models.Task, error) {
	var tasks []models.Task
	err := s.db.Preload("AssignedUser").Preload("Creator").Order("status, position").Find(&tasks).Error
	return tasks, err
}

// GetTaskByID 根据ID获取任务
func (s *TaskService) GetTaskByID(id string) (*models.Task, error) {
	var task models.Task
	err := s.db.First(&task, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetTaskByIDWithUsers 根据ID获取任务及关联用户信息
func (s *TaskService) GetTaskByIDWithUsers(id string) (*models.Task, error) {
	var task models.Task
	err := s.db.Preload("AssignedUser").Preload("Creator").First(&task, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// CreateTask 创建新任务
func (s *TaskService) CreateTask(req models.TaskCreateRequest) (*models.Task, error) {
	// 开始事务，确保位置更新的原子性
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 将现有任务的位置全部+1，为新任务腾出position=0的位置
	err := tx.Model(&models.Task{}).Where("status = ?", models.StatusTodo).Update("position", gorm.Expr("position + 1")).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新现有任务位置失败: %v", err)
	}

	task := models.Task{
		Title:          req.Title,
		Description:    req.Description,
		Status:         models.StatusTodo,
		Priority:       req.Priority,
		Type:           req.Type,
		VersionID:      req.VersionID,
		Position:       0, // 新任务始终放在最上面
		DueDate:        convertCustomDateToTime(req.DueDate),
		StartDate:      convertCustomDateToTime(req.StartDate),
		EndDate:        convertCustomDateToTime(req.EndDate),
		EstimatedHours: req.EstimatedHours,
		ParentTaskID:   req.ParentTaskID,
		AssignedTo:     req.AssignedTo,
		CreatedBy:      req.CreatedBy,
	}

	// 设置默认优先级
	if task.Priority == "" {
		task.Priority = models.PriorityMedium
	}

	// 设置默认任务类型
	if task.Type == "" {
		task.Type = models.TypeTask
	}

	// 设置标签
	if err := task.SetTags(req.Tags); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 创建任务
	if err = tx.Create(&task).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return &task, nil
}

// UpdateTask 更新任务信息
func (s *TaskService) UpdateTask(id string, req models.TaskUpdateRequest) (*models.Task, error) {
	task, err := s.GetTaskByID(id)
	if err != nil {
		return nil, err
	}

	// 验证标题
	if req.Title != "" && len(req.Title) > 255 {
		return nil, fmt.Errorf("标题长度不能超过255个字符")
	}

	// 更新字段
	if req.Title != "" {
		task.Title = req.Title
	}
	task.Description = req.Description
	if req.Priority != "" {
		task.Priority = req.Priority
	}
	if req.Type != "" {
		task.Type = req.Type
	}
	task.VersionID = req.VersionID

	// 更新新字段
	if req.DueDate != nil {
		task.DueDate = &req.DueDate.Time
	}
	if req.StartDate != nil {
		task.StartDate = &req.StartDate.Time
	}
	if req.EndDate != nil {
		task.EndDate = &req.EndDate.Time
	}
	task.EstimatedHours = req.EstimatedHours
	task.ActualHours = req.ActualHours
	task.AssignedTo = req.AssignedTo

	// 设置标签
	if err := task.SetTags(req.Tags); err != nil {
		return nil, err
	}

	err = s.db.Save(task).Error
	if err != nil {
		return nil, err
	}

	return task, nil
}

// DeleteTask 删除任务
func (s *TaskService) DeleteTask(id string) error {
	return s.db.Delete(&models.Task{}, "id = ?", id).Error
}

// UpdateTaskStatus 更新任务状态
func (s *TaskService) UpdateTaskStatus(id string, status models.TaskStatus) (*models.Task, error) {
	task, err := s.GetTaskByID(id)
	if err != nil {
		return nil, err
	}

	// 如果状态改变，需要重新计算位置
	if task.Status != status {
		// 获取目标状态下的最大位置
		var maxPosition int
		s.db.Model(&models.Task{}).Where("status = ?", status).Select("COALESCE(MAX(position), -1)").Scan(&maxPosition)

		task.Status = status
		task.Position = maxPosition + 1
	}

	err = s.db.Save(task).Error
	if err != nil {
		return nil, err
	}

	return task, nil
}

// UpdateTaskPosition 更新任务位置和状态
func (s *TaskService) UpdateTaskPosition(id string, status models.TaskStatus, position int) (*models.Task, error) {
	task, err := s.GetTaskByID(id)
	if err != nil {
		return nil, err
	}

	oldStatus := task.Status
	oldPosition := task.Position

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 如果状态改变
	if oldStatus != status {
		// 在原状态中，将后面的任务位置前移
		tx.Model(&models.Task{}).
			Where("status = ? AND position > ?", oldStatus, oldPosition).
			Update("position", gorm.Expr("position - 1"))

		// 在新状态中，将指定位置及后面的任务位置后移
		tx.Model(&models.Task{}).
			Where("status = ? AND position >= ?", status, position).
			Update("position", gorm.Expr("position + 1"))
	} else {
		// 同一状态内移动
		if position > oldPosition {
			// 向后移动：将中间的任务位置前移
			tx.Model(&models.Task{}).
				Where("status = ? AND position > ? AND position <= ?", status, oldPosition, position).
				Update("position", gorm.Expr("position - 1"))
		} else if position < oldPosition {
			// 向前移动：将中间的任务位置后移
			tx.Model(&models.Task{}).
				Where("status = ? AND position >= ? AND position < ?", status, position, oldPosition).
				Update("position", gorm.Expr("position + 1"))
		}
	}

	// 更新当前任务
	task.Status = status
	task.Position = position
	if err := tx.Save(task).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return task, nil
}

// BulkDeleteTasks 批量删除任务
func (s *TaskService) BulkDeleteTasks(taskIDs []string) error {
	if len(taskIDs) == 0 {
		return nil
	}

	return s.db.Where("id IN ?", taskIDs).Delete(&models.Task{}).Error
}

// BulkUpdateTaskStatus 批量更新任务状态
func (s *TaskService) BulkUpdateTaskStatus(taskIDs []string, status models.TaskStatus) ([]models.Task, error) {
	if len(taskIDs) == 0 {
		return []models.Task{}, nil
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var tasks []models.Task

	// 获取所有要更新的任务
	if err := tx.Where("id IN ?", taskIDs).Find(&tasks).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 为每个任务计算新位置
	for i := range tasks {
		if tasks[i].Status != status {
			// 获取目标状态下的最大位置
			var maxPosition int
			tx.Model(&models.Task{}).Where("status = ?", status).Select("COALESCE(MAX(position), -1)").Scan(&maxPosition)

			tasks[i].Status = status
			tasks[i].Position = maxPosition + i + 1 // 确保每个任务有不同的位置
		}
	}

	// 批量更新
	for _, task := range tasks {
		if err := tx.Save(&task).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return tasks, nil
}

// GetTaskStatistics 获取任务统计信息
func (s *TaskService) GetTaskStatistics() (*models.TaskStatistics, error) {
	var stats models.TaskStatistics

	// 获取总任务数
	var totalCount int64
	s.db.Model(&models.Task{}).Count(&totalCount)
	stats.TotalTasks = int(totalCount)

	// 按状态统计
	stats.TasksByStatus = make(map[models.TaskStatus]int)
	var statusCounts []struct {
		Status models.TaskStatus
		Count  int
	}

	s.db.Model(&models.Task{}).
		Select("status, count(*) as count").
		Group("status").
		Scan(&statusCounts)

	for _, sc := range statusCounts {
		stats.TasksByStatus[sc.Status] = sc.Count
		switch sc.Status {
		case models.StatusTodo:
			stats.TodoTasks = sc.Count
		case models.StatusInProgress:
			stats.InProgressTasks = sc.Count
		case models.StatusDone:
			stats.CompletedTasks = sc.Count
		}
	}

	// 按优先级统计
	stats.TasksByPriority = make(map[models.TaskPriority]int)
	var priorityCounts []struct {
		Priority models.TaskPriority
		Count    int
	}

	s.db.Model(&models.Task{}).
		Select("priority, count(*) as count").
		Group("priority").
		Scan(&priorityCounts)

	for _, pc := range priorityCounts {
		stats.TasksByPriority[pc.Priority] = pc.Count
	}

	// 计算完成率
	if stats.TotalTasks > 0 {
		stats.CompletionRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
	}

	// 生成模拟的最近活动（实际项目中应该有专门的活动日志表）
	stats.RecentActivity = s.generateRecentActivity()

	// 生成模拟的生产力趋势数据
	stats.ProductivityTrend = s.generateProductivityTrend()

	return &stats, nil
}

// generateRecentActivity 生成最近活动数据（模拟）
func (s *TaskService) generateRecentActivity() []models.TaskActivity {
	var tasks []models.Task
	s.db.Order("updated_at DESC").Limit(10).Find(&tasks)

	var activities []models.TaskActivity
	for _, task := range tasks {
		activity := models.TaskActivity{
			TaskID:      task.ID,
			TaskTitle:   task.Title,
			Action:      "updated",
			Timestamp:   task.UpdatedAt,
			Description: "任务状态更新为 " + task.Status.GetDisplayName(),
		}
		activities = append(activities, activity)
	}

	return activities
}

// generateProductivityTrend 生成生产力趋势数据（模拟）
func (s *TaskService) generateProductivityTrend() []models.ProductivityData {
	var trend []models.ProductivityData

	// 生成最近7天的数据
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")

		var created, completed int64

		// 统计当天创建的任务
		s.db.Model(&models.Task{}).
			Where("DATE(created_at) = ?", dateStr).
			Count(&created)

		// 统计当天完成的任务（状态为done且更新时间在当天）
		s.db.Model(&models.Task{}).
			Where("status = ? AND DATE(updated_at) = ?", models.StatusDone, dateStr).
			Count(&completed)

		productivity := float64(0)
		if created > 0 {
			productivity = float64(completed) / float64(created) * 100
		}

		trend = append(trend, models.ProductivityData{
			Date:           dateStr,
			TasksCreated:   int(created),
			TasksCompleted: int(completed),
			Productivity:   productivity,
		})
	}

	return trend
}
