# 项目管理看板系统 - 部署指南 🚀

## 📋 系统要求

### 最低配置
- **CPU**: 1核心
- **内存**: 512MB RAM
- **存储**: 100MB 可用空间
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)

### 推荐配置
- **CPU**: 2核心或更多
- **内存**: 2GB RAM 或更多
- **存储**: 1GB 可用空间
- **操作系统**: 最新版本

## 🛠️ 环境准备

### 1. 安装 Go 语言
```bash
# 下载并安装 Go 1.21 或更高版本
# 访问: https://golang.org/dl/

# 验证安装
go version
```

### 2. 克隆项目
```bash
git clone <repository-url>
cd projectm2
```

### 3. 安装依赖
```bash
go mod tidy
```

## 🚀 快速部署

### 开发环境部署
```bash
# 启动开发服务器
go run main.go

# 访问应用
# http://localhost:8080
```

### 生产环境部署

#### 1. 编译应用
```bash
# Windows
go build -o projectm2.exe .

# Linux/macOS
go build -o projectm2 .
```

#### 2. 配置环境变量
```bash
# 设置生产模式
export GIN_MODE=release

# 设置端口 (可选)
export PORT=8080
```

#### 3. 启动应用
```bash
# Windows
./projectm2.exe

# Linux/macOS
./projectm2
```

## 🐳 Docker 部署

### 1. 创建 Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/web ./web

EXPOSE 8080
CMD ["./main"]
```

### 2. 构建镜像
```bash
docker build -t projectm2 .
```

### 3. 运行容器
```bash
docker run -d \
  --name projectm2 \
  -p 8080:8080 \
  -v $(pwd)/data:/root/data \
  projectm2
```

## ☁️ 云平台部署

### Heroku 部署
1. 创建 `Procfile`:
```
web: ./projectm2
```

2. 部署命令:
```bash
heroku create your-app-name
git push heroku main
```

### Vercel 部署
1. 安装 Vercel CLI:
```bash
npm i -g vercel
```

2. 部署:
```bash
vercel --prod
```

### Railway 部署
1. 连接 GitHub 仓库
2. 选择项目
3. 自动部署

## 🔧 配置选项

### 环境变量
```bash
# 服务器端口
PORT=8080

# 运行模式
GIN_MODE=release

# 数据库文件路径
DB_PATH=./projectm2.db

# 静态文件路径
STATIC_PATH=./web/static

# 模板文件路径
TEMPLATE_PATH=./web/templates
```

### 配置文件 (config.yaml)
```yaml
server:
  port: 8080
  mode: release

database:
  path: ./projectm2.db
  
static:
  path: ./web/static
  
templates:
  path: ./web/templates
```

## 🔒 安全配置

### 1. HTTPS 配置
```go
// 在 main.go 中添加
r.RunTLS(":8443", "cert.pem", "key.pem")
```

### 2. 反向代理 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --add-port=8080/tcp --permanent
```

## 📊 监控与日志

### 1. 日志配置
```go
// 添加日志中间件
r.Use(gin.Logger())
r.Use(gin.Recovery())
```

### 2. 健康检查
```go
// 添加健康检查端点
r.GET("/health", func(c *gin.Context) {
    c.JSON(200, gin.H{"status": "ok"})
})
```

### 3. 性能监控
- 使用 Prometheus + Grafana
- 集成 APM 工具 (如 New Relic)

## 🔄 数据备份

### 自动备份脚本
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup"
DB_FILE="projectm2.db"

# 创建备份
cp $DB_FILE $BACKUP_DIR/projectm2_$DATE.db

# 保留最近7天的备份
find $BACKUP_DIR -name "projectm2_*.db" -mtime +7 -delete
```

### 定时备份 (Cron)
```bash
# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

## 🚀 性能优化

### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
```

### 2. 静态文件缓存
```go
// 设置静态文件缓存
r.Static("/static", "./web/static")
r.StaticFile("/favicon.ico", "./web/static/favicon.ico")
```

### 3. Gzip 压缩
```go
import "github.com/gin-contrib/gzip"

r.Use(gzip.Gzip(gzip.DefaultCompression))
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :8080
# 或
lsof -i :8080

# 终止进程
kill -9 <PID>
```

2. **数据库文件权限**
```bash
# 设置正确权限
chmod 644 projectm2.db
chown app:app projectm2.db
```

3. **静态文件404**
```bash
# 检查文件路径
ls -la web/static/
```

### 日志分析
```bash
# 查看应用日志
tail -f /var/log/projectm2.log

# 查看错误日志
grep "ERROR" /var/log/projectm2.log
```

## 📈 扩展部署

### 负载均衡
```nginx
upstream projectm2 {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    location / {
        proxy_pass http://projectm2;
    }
}
```

### 数据库集群
- 使用 PostgreSQL 替代 SQLite
- 配置主从复制
- 实现读写分离

## ✅ 部署检查清单

- [ ] Go 环境安装完成
- [ ] 项目代码下载
- [ ] 依赖安装成功
- [ ] 应用编译通过
- [ ] 数据库初始化
- [ ] 静态文件可访问
- [ ] 端口配置正确
- [ ] 防火墙规则设置
- [ ] SSL 证书配置 (生产环境)
- [ ] 备份策略实施
- [ ] 监控系统部署
- [ ] 性能测试通过

## 🎉 部署完成

恭喜！您的项目管理看板系统已成功部署！

**访问地址**: http://your-domain.com:8080
**管理后台**: http://your-domain.com:8080/admin (如果配置)
**API文档**: http://your-domain.com:8080/api/docs (如果配置)

享受您的全新项目管理体验！ 🚀
