<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ProjectM - 现代化项目管理系统">
    <title>{{.title}} - ProjectM</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="/static/css/tailwind-local.css">
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 继承登录页面的样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f0f23;
            overflow: hidden;
        }

        /* 主容器 - 分屏设计 */
        .register-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* 左侧装饰区域 */
        .register-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .register-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            animation: shimmer 8s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        /* 左侧品牌区域 */
        .brand-section {
            text-align: center;
            z-index: 10;
            position: relative;
        }

        .brand-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .brand-logo svg {
            width: 60px;
            height: 60px;
            color: white;
        }

        .brand-title {
            font-size: 3rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .brand-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            line-height: 1.6;
        }

        /* 右侧注册区域 */
        .register-right {
            flex: 1;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            position: relative;
        }

        /* 注册卡片 */
        .register-card {
            width: 100%;
            max-width: 480px;
            background: white;
            border-radius: 24px;
            padding: 3rem;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 8px 25px rgba(0, 0, 0, 0.06);
            position: relative;
        }

        .register-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .register-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.5rem;
        }

        .register-subtitle {
            color: #64748b;
            font-size: 1rem;
            font-weight: 400;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #ffffff;
            font-size: 1rem;
            color: #1f2937;
            transition: all 0.2s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-input::placeholder {
            color: #a0aec0;
            font-weight: 400;
        }

        .register-btn {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.025em;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.3);
        }

        .register-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.4);
        }

        .register-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.3);
        }

        .register-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .error-message {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 1px solid #f87171;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .success-message {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 1px solid #34d399;
            color: #059669;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            animation: slideDown 0.4s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-12px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .theme-toggle {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 20;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .register-wrapper {
                flex-direction: column;
            }

            .register-left {
                min-height: 40vh;
                padding: 2rem;
            }

            .brand-title {
                font-size: 2rem;
            }

            .brand-subtitle {
                font-size: 1rem;
            }

            .register-right {
                padding: 2rem 1.5rem;
            }

            .register-card {
                padding: 2rem;
            }

            .theme-toggle {
                top: 16px;
                right: 16px;
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .register-left {
                min-height: 30vh;
                padding: 1.5rem;
            }

            .brand-logo {
                width: 80px;
                height: 80px;
            }

            .brand-title {
                font-size: 1.75rem;
            }

            .register-card {
                padding: 1.5rem;
            }

            .register-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <button id="themeToggle" class="theme-toggle" title="切换主题">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
        </svg>
    </button>

    <!-- 全新分屏注册界面 -->
    <div class="register-wrapper">
        <!-- 左侧品牌区域 -->
        <div class="register-left">
            <div class="brand-section">
                <div class="brand-logo">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                    </svg>
                </div>
                <h1 class="brand-title">ProjectM</h1>
                <p class="brand-subtitle">现代化项目管理平台<br>加入我们，开启高效协作</p>
            </div>
        </div>

        <!-- 右侧注册区域 -->
        <div class="register-right">
            <div class="register-card">
                <div class="register-header">
                    <h2 class="register-title">创建账户</h2>
                    <p class="register-subtitle">填写信息完成注册，等待管理员审核</p>
                </div>

                <!-- 错误/成功消息 -->
                <div id="message-container" class="mb-6 hidden">
                    <div id="message" class="p-4 rounded-xl text-sm font-medium"></div>
                </div>

                <!-- 注册表单 -->
                <form id="registerForm" class="space-y-6">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            required
                            class="form-input"
                            placeholder="请输入用户名（3-50个字符）"
                            autocomplete="username"
                        >
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">邮箱</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            required
                            class="form-input"
                            placeholder="请输入邮箱地址"
                            autocomplete="email"
                        >
                    </div>

                    <div class="form-group">
                        <label for="fullName" class="form-label">姓名</label>
                        <input
                            type="text"
                            id="fullName"
                            name="fullName"
                            required
                            class="form-input"
                            placeholder="请输入真实姓名"
                            autocomplete="name"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            class="form-input"
                            placeholder="请输入密码（至少6位）"
                            autocomplete="new-password"
                        >
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" class="form-label">确认密码</label>
                        <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            required
                            class="form-input"
                            placeholder="请再次输入密码"
                            autocomplete="new-password"
                        >
                    </div>

                    <button
                        type="submit"
                        id="registerBtn"
                        class="register-btn"
                    >
                        <span id="registerText">注册</span>
                        <div id="registerSpinner" class="loading-spinner hidden"></div>
                    </button>
                </form>

                <!-- 底部信息 -->
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-600 mb-6">
                        已有账户？
                        <a href="/login" class="text-blue-600 hover:text-blue-700 font-semibold transition-colors">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="/static/js/register.js"></script>
</body>
</html>
