package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TaskVersion 任务版本模型
type TaskVersion struct {
	ID          string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string         `json:"name" gorm:"not null;size:100;uniqueIndex"`
	DisplayName string         `json:"display_name" gorm:"not null;size:100"`
	Description string         `json:"description" gorm:"type:text"`
	Color       string         `json:"color" gorm:"not null;size:7;default:'#3B82F6'"`
	Icon        string         `json:"icon" gorm:"not null;size:10;default:'📋'"`
	IsActive    bool           `json:"is_active" gorm:"not null;default:true"`
	SortOrder   int            `json:"sort_order" gorm:"not null;default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Tasks []Task `json:"tasks,omitempty" gorm:"foreignKey:VersionID"`
}

// BeforeCreate 创建前钩子
func (tv *TaskVersion) BeforeCreate(tx *gorm.DB) error {
	if tv.ID == "" {
		tv.ID = uuid.New().String()
	}
	return nil
}

// TaskVersionCreateRequest 创建任务版本请求结构
type TaskVersionCreateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	Color       string `json:"color" binding:"required,len=7"`
	Icon        string `json:"icon" binding:"required,min=1,max=10"`
	SortOrder   int    `json:"sort_order"`
}

// TaskVersionUpdateRequest 更新任务版本请求结构
type TaskVersionUpdateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	Color       string `json:"color" binding:"required,len=7"`
	Icon        string `json:"icon" binding:"required,min=1,max=10"`
	IsActive    *bool  `json:"is_active"`
	SortOrder   int    `json:"sort_order"`
}

// TaskVersionResponse 任务版本响应结构
type TaskVersionResponse struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	Icon        string    `json:"icon"`
	IsActive    bool      `json:"is_active"`
	SortOrder   int       `json:"sort_order"`
	TaskCount   int64     `json:"task_count,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应结构
func (tv *TaskVersion) ToResponse() TaskVersionResponse {
	return TaskVersionResponse{
		ID:          tv.ID,
		Name:        tv.Name,
		DisplayName: tv.DisplayName,
		Description: tv.Description,
		Color:       tv.Color,
		Icon:        tv.Icon,
		IsActive:    tv.IsActive,
		SortOrder:   tv.SortOrder,
		CreatedAt:   tv.CreatedAt,
		UpdatedAt:   tv.UpdatedAt,
	}
}
