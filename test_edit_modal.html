<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试编辑模态框</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        input, select { margin: 5px; padding: 5px; }
        button { margin: 5px; padding: 10px; background: #007cba; color: white; border: none; cursor: pointer; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>测试编辑模态框数据回显</h1>
    
    <div class="test-section">
        <h3>模拟任务数据</h3>
        <div class="log" id="taskData"></div>
    </div>
    
    <div class="test-section">
        <h3>模拟表单字段</h3>
        <label>开始时间：</label>
        <input type="date" id="taskStartDate" name="start_date">
        <br>
        <label>结束时间：</label>
        <input type="date" id="taskEndDate" name="end_date">
        <br>
        <label>分配给：</label>
        <select id="taskAssignee" name="assignee">
            <option value="">未分配</option>
            <option value="fc358eea-3243-42aa-89bc-76863c833384">王明</option>
            <option value="other-user-id">其他用户</option>
        </select>
        <br>
        <button onclick="testSetFields()">测试设置字段</button>
        <button onclick="clearFields()">清空字段</button>
    </div>
    
    <div class="test-section">
        <h3>测试日志</h3>
        <div class="log" id="testLog"></div>
    </div>

    <script>
        // 模拟任务数据
        const mockTaskData = {
            id: "672d9afc-8c0c-4643-948f-cc53f616e25b",
            title: "漏洞扫描，ssl漏洞排查",
            assigned_to: "fc358eea-3243-42aa-89bc-76863c833384",
            start_date: "2025-07-07T03:00:00Z",
            end_date: "2025-07-10T00:00:00Z",
            priority: "high",
            type: "bug"
        };

        function log(message) {
            const logDiv = document.getElementById('testLog');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }

        function displayTaskData() {
            document.getElementById('taskData').innerHTML = JSON.stringify(mockTaskData, null, 2);
        }

        function testSetFields() {
            log('开始测试设置字段...');
            
            // 测试开始日期
            if (mockTaskData.start_date) {
                const startDate = new Date(mockTaskData.start_date);
                const startDateStr = startDate.toISOString().slice(0, 10);
                document.getElementById('taskStartDate').value = startDateStr;
                log('✅ 开始日期已设置: ' + startDateStr);
            }
            
            // 测试结束日期
            if (mockTaskData.end_date) {
                const endDate = new Date(mockTaskData.end_date);
                const endDateStr = endDate.toISOString().slice(0, 10);
                document.getElementById('taskEndDate').value = endDateStr;
                log('✅ 结束日期已设置: ' + endDateStr);
            }
            
            // 测试分配用户
            if (mockTaskData.assigned_to) {
                const assigneeSelect = document.getElementById('taskAssignee');
                assigneeSelect.value = mockTaskData.assigned_to;
                log('✅ 分配用户已设置: ' + mockTaskData.assigned_to);
                log('当前选择框值: ' + assigneeSelect.value);
            }
            
            log('字段设置完成！');
        }

        function clearFields() {
            document.getElementById('taskStartDate').value = '';
            document.getElementById('taskEndDate').value = '';
            document.getElementById('taskAssignee').value = '';
            log('字段已清空');
        }

        // 页面加载时显示任务数据
        window.onload = function() {
            displayTaskData();
            log('页面加载完成，准备测试');
        };
    </script>
</body>
</html>
