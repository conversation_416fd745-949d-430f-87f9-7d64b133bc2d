<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>

    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-gray-900">{{.title}}</h1>
                            <p class="text-xs text-gray-500">现代化项目管理系统</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"></path>
                        </svg>
                        返回看板
                    </a>

                    <!-- 当前用户信息 -->
                    <div id="currentUserInfo" class="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium" id="userAvatar">
                            U
                        </div>
                        <div class="text-xs">
                            <div class="font-medium text-gray-900" id="userDisplayName">加载中...</div>
                            <div class="text-gray-500 text-xs" id="userRole">-</div>
                        </div>
                    </div>

                    <button id="logoutBtn" class="p-2 rounded-lg bg-red-100 hover:bg-red-200 transition-colors duration-200" title="退出登录">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- 用户管理页面 -->
<div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">用户管理</h2>
            <p class="text-gray-600 mt-1">管理系统用户和权限</p>
        </div>
        <button id="addUserBtn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            新建用户
        </button>
    </div>

    <!-- 用户统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 p-4 rounded-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">总用户数</p>
                    <p id="totalUsers" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">活跃用户</p>
                    <p id="activeUsers" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
        <div class="bg-red-50 p-4 rounded-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">管理员</p>
                    <p id="adminUsers" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
        <div class="bg-yellow-50 p-4 rounded-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">待审核</p>
                    <p id="pendingUsers" class="text-2xl font-semibold text-gray-900">0</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="flex flex-col sm:flex-row gap-4 mb-6">
        <div class="flex-1">
            <input type="text" id="userSearch" placeholder="搜索用户名、邮箱或姓名..." 
                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
        </div>
        <div class="flex gap-2">
            <select id="roleFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">所有角色</option>
                <option value="admin">管理员</option>
                <option value="manager">项目经理</option>
                <option value="developer">开发者</option>
                <option value="viewer">查看者</option>
            </select>
            <select id="statusFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">所有状态</option>
                <option value="pending">待审核</option>
                <option value="active">已激活</option>
                <option value="rejected">已拒绝</option>
                <option value="disabled">已禁用</option>
            </select>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        用户信息
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        角色
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        最后登录
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">操作</span>
                    </th>
                </tr>
            </thead>
            <tbody id="userTableBody" class="bg-white divide-y divide-gray-200">
                <!-- 用户数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </button>
            <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    显示第 <span id="startRecord" class="font-medium">1</span> 到 <span id="endRecord" class="font-medium">10</span> 条，
                    共 <span id="totalRecords" class="font-medium">0</span> 条记录
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 用户管理模态框 -->
<div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999] flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[95vh] overflow-y-auto relative z-[10000]">
            <form id="userForm">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="userModalTitle">
                                新建用户
                            </h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="userName" class="block text-sm font-medium text-gray-700 mb-2">
                                        用户名 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="userName" name="username" required maxlength="50"
                                           class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="请输入用户名（3-50字符）">
                                </div>
                                <div>
                                    <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-2">
                                        邮箱 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" id="userEmail" name="email" required
                                           class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="请输入邮箱地址">
                                </div>
                                <div id="passwordFields">
                                    <div>
                                        <label for="userPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                            密码 <span class="text-red-500">*</span>
                                        </label>
                                        <input type="password" id="userPassword" name="password" required minlength="6" maxlength="100"
                                               class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                               placeholder="请输入密码（至少6位字符）">
                                        <p class="mt-1 text-xs text-gray-500">密码长度至少6位，建议包含字母、数字和特殊字符</p>
                                    </div>
                                    <div>
                                        <label for="userPasswordConfirm" class="block text-sm font-medium text-gray-700 mb-2">
                                            确认密码 <span class="text-red-500">*</span>
                                        </label>
                                        <input type="password" id="userPasswordConfirm" name="password_confirm" required minlength="6" maxlength="100"
                                               class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                               placeholder="请再次输入密码确认">
                                        <p id="passwordMatchMessage" class="mt-1 text-xs hidden"></p>
                                    </div>
                                </div>
                                <div>
                                    <label for="userFullName" class="block text-sm font-medium text-gray-700 mb-2">
                                        姓名 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="userFullName" name="full_name" required maxlength="100"
                                           class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="请输入真实姓名">
                                </div>
                                <div>
                                    <label for="userRoleSelect" class="block text-sm font-medium text-gray-700 mb-2">角色</label>
                                    <select id="userRoleSelect" name="role"
                                            class="form-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="developer">🧑‍💻 开发者</option>
                                        <option value="manager">👨‍💼 项目经理</option>
                                        <option value="admin">👑 管理员</option>
                                        <option value="viewer">👁️ 查看者</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">头像</label>
                                    <div class="flex items-center space-x-4">
                                        <!-- 头像预览 -->
                                        <div class="flex-shrink-0">
                                            <img id="avatarPreview" class="h-16 w-16 rounded-full object-cover border-2 border-gray-300"
                                                 src="" alt="头像预览" style="display: none;">
                                            <div id="avatarPlaceholder" class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm border-2 border-gray-300">
                                                无头像
                                            </div>
                                        </div>
                                        <!-- 上传控件 -->
                                        <div class="flex-1">
                                            <input type="file" id="avatarFile" accept="image/*" class="hidden">
                                            <input type="hidden" id="userAvatar" name="avatar" value="">
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectAvatarFile()"
                                                        class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm font-medium transition-colors">
                                                    选择图片
                                                </button>
                                                <button type="button" onclick="removeAvatar()" id="removeAvatarBtn" style="display: none;"
                                                        class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md text-sm font-medium transition-colors">
                                                    删除
                                                </button>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">支持 JPG、PNG、GIF、BMP 格式，最大 5MB</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        保存
                    </button>
                    <button type="button" id="cancelUserBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
    </main>

<script src="/static/js/users.js?v=3"></script>
<script>
    // 退出登录功能
    document.addEventListener('DOMContentLoaded', function() {
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                if (confirm('确定要退出登录吗？')) {
                    fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 退出成功，跳转到登录页
                            window.location.href = '/login';
                        } else {
                            alert('退出失败：' + (data.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('退出登录错误:', error);
                        // 即使出错也跳转到登录页，因为可能是网络问题
                        window.location.href = '/login';
                    });
                }
            });
        }

        // 加载当前用户信息
        loadCurrentUserInfo();
    });

    // 加载当前用户信息
    function loadCurrentUserInfo() {
        fetch('/api/auth/user')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.user) {
                    updateUserDisplay(data.user);
                } else {
                    console.error('获取用户信息失败:', data.message);
                }
            })
            .catch(error => {
                console.error('获取用户信息错误:', error);
            });
    }

    // 更新用户显示
    function updateUserDisplay(user) {
        const userAvatar = document.getElementById('userAvatar');
        const userDisplayName = document.getElementById('userDisplayName');
        const userRole = document.getElementById('userRole');

        if (userAvatar && user.full_name) {
            userAvatar.textContent = user.full_name.charAt(0).toUpperCase();
        }

        if (userDisplayName) {
            userDisplayName.textContent = user.full_name || user.username || '未知用户';
        }

        if (userRole) {
            const roleNames = {
                'admin': '管理员',
                'developer': '开发者',
                'tester': '测试员',
                'viewer': '查看者'
            };
            userRole.textContent = roleNames[user.role] || user.role || '用户';
        }
    }
</script>
</body>
</html>
