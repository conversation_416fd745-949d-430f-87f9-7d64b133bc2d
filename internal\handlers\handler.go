package handlers

import (
	"log"
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Handler 处理器结构
type Handler struct {
	taskService         *services.TaskService
	attachmentService   *services.AttachmentService
	AttachmentHandler   *AttachmentHandler
	UserHandler         *UserHandler
	TaskTypeHandler     *TaskTypeHandler
	TaskVersionHandler  *TaskVersionHandler
	TaskTemplateHandler *TaskTemplateHandler
}

// NewHandler 创建新的处理器实例
func NewHandler(db *gorm.DB) *Handler {
	return &Handler{
		taskService:         services.NewTaskService(db),
		attachmentService:   services.NewAttachmentService(db),
		AttachmentHandler:   NewAttachmentHandler(db),
		UserHandler:         NewUserHandler(db),
		TaskTypeHandler:     NewTaskTypeHandler(services.NewTaskTypeService(db)),
		TaskVersionHandler:  NewTaskVersionHandler(services.NewTaskVersionService(db)),
		TaskTemplateHandler: NewTaskTemplateHandler(services.NewTaskTemplateService(db)),
	}
}

// Home 主页处理器
func (h *Handler) Home(c *gin.Context) {
	log.Println("Home handler called")
	// 获取所有任务（包含用户信息）
	tasks, err := h.taskService.GetAllTasksWithUsers()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "获取任务失败",
		})
		return
	}

	// 按状态分组任务
	tasksByStatus := map[models.TaskStatus][]models.Task{
		models.StatusTodo:       {},
		models.StatusInProgress: {},
		models.StatusDone:       {},
	}

	for _, task := range tasks {
		tasksByStatus[task.Status] = append(tasksByStatus[task.Status], task)
	}

	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":            "项目管理看板",
		"tasksByStatus":    tasksByStatus,
		"statusTodo":       models.StatusTodo,
		"statusInProgress": models.StatusInProgress,
		"statusDone":       models.StatusDone,
	})
}

// UsersPage 用户管理页面处理器
func (h *Handler) UsersPage(c *gin.Context) {
	c.HTML(http.StatusOK, "users.html", gin.H{
		"title": "用户管理",
	})
}

// GetTasks 获取所有任务API
func (h *Handler) GetTasks(c *gin.Context) {
	tasks, err := h.taskService.GetAllTasksWithUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"tasks": tasks})
}

// GetTaskByID 根据ID获取任务详情API
func (h *Handler) GetTaskByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	task, err := h.taskService.GetTaskByIDWithUsers(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task": task})
}

// GetCurrentUser 获取当前用户API
func (h *Handler) GetCurrentUser(c *gin.Context) {
	// 返回默认用户信息（后续可以实现真正的用户认证）
	currentUser := gin.H{
		"id":       "default-user",
		"username": "admin",
		"fullName": "管理员",
		"email":    "<EMAIL>",
		"role":     "admin",
		"avatar":   "",
		"isActive": true,
	}

	c.JSON(http.StatusOK, currentUser)
}

// GetSettings 获取系统设置API
func (h *Handler) GetSettings(c *gin.Context) {
	// 返回默认设置
	settings := gin.H{
		"theme":              "light",
		"language":           "zh-CN",
		"notifications":      true,
		"autoSave":           true,
		"taskReminders":      true,
		"emailNotifications": false,
		"timezone":           "Asia/Shanghai",
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateSettings 更新系统设置API
func (h *Handler) UpdateSettings(c *gin.Context) {
	var settings map[string]interface{}
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的设置数据"})
		return
	}

	// 这里可以实现设置的持久化存储
	// 目前只返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"message":  "设置更新成功",
		"settings": settings,
	})
}

// GetSyncStatus 获取同步状态API
func (h *Handler) GetSyncStatus(c *gin.Context) {
	// 返回同步状态信息
	syncStatus := gin.H{
		"status":         "connected",
		"lastSync":       "2025-06-30T18:25:00Z",
		"lastModified":   1719764700000, // Unix timestamp in milliseconds
		"pendingChanges": 0,
		"isOnline":       true,
	}

	c.JSON(http.StatusOK, syncStatus)
}

// GetSyncChanges 获取同步变更API
func (h *Handler) GetSyncChanges(c *gin.Context) {
	since := c.Query("since")

	// 返回空的变更列表（基础实现）
	changes := gin.H{
		"changes":   []interface{}{},
		"hasMore":   false,
		"timestamp": 1719764700000,
	}

	// 如果有since参数，可以根据时间戳过滤
	if since != "" && since != "null" {
		// 这里可以实现基于时间戳的变更查询
		// 目前返回空列表
	}

	c.JSON(http.StatusOK, changes)
}

// CreateTask 创建任务API
func (h *Handler) CreateTask(c *gin.Context) {
	var req models.TaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从认证中间件获取当前用户信息
	userInterface, exists := c.Get("user")
	if exists {
		user := userInterface.(*models.User)
		if user != nil {
			req.CreatedBy = &user.ID
		}
	}

	task, err := h.taskService.CreateTask(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建任务失败"})
		return
	}

	// 检查是否有临时附件需要更新
	if req.TempTaskID != "" {
		// 更新临时附件的任务ID
		if err := h.attachmentService.UpdateTempAttachmentsTaskID(req.TempTaskID, task.ID); err != nil {
			log.Printf("更新临时附件失败: %v", err)
			// 不影响任务创建，只记录日志
		}
	}

	c.JSON(http.StatusCreated, gin.H{"task": task})
}

// UpdateTask 更新任务API
func (h *Handler) UpdateTask(c *gin.Context) {
	id := c.Param("id")
	var req models.TaskUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("UpdateTask binding error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	task, err := h.taskService.UpdateTask(id, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task": task})
}

// DeleteTask 删除任务API
func (h *Handler) DeleteTask(c *gin.Context) {
	id := c.Param("id")

	err := h.taskService.DeleteTask(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务删除成功"})
}

// UpdateTaskStatus 更新任务状态API
func (h *Handler) UpdateTaskStatus(c *gin.Context) {
	id := c.Param("id")
	var req models.TaskStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	task, err := h.taskService.UpdateTaskStatus(id, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新任务状态失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task": task})
}

// UpdateTaskPosition 更新任务位置API
func (h *Handler) UpdateTaskPosition(c *gin.Context) {
	id := c.Param("id")
	var req models.TaskPositionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	task, err := h.taskService.UpdateTaskPosition(id, req.Status, req.Position)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新任务位置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"task": task})
}

// BulkDeleteTasks 批量删除任务API
func (h *Handler) BulkDeleteTasks(c *gin.Context) {
	var req struct {
		TaskIDs []string `json:"task_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.taskService.BulkDeleteTasks(req.TaskIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批量删除任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量删除成功", "deleted_count": len(req.TaskIDs)})
}

// BulkUpdateTaskStatus 批量更新任务状态API
func (h *Handler) BulkUpdateTaskStatus(c *gin.Context) {
	var req struct {
		TaskIDs []string          `json:"task_ids" binding:"required"`
		Status  models.TaskStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	tasks, err := h.taskService.BulkUpdateTaskStatus(req.TaskIDs, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批量更新任务状态失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量更新成功", "tasks": tasks})
}

// GetTaskStatistics 获取任务统计信息API
func (h *Handler) GetTaskStatistics(c *gin.Context) {
	stats, err := h.taskService.GetTaskStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计信息失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"statistics": stats})
}

// GetTimePresets 获取时间预设选项API
func (h *Handler) GetTimePresets(c *gin.Context) {
	presets := models.GetTimePresets()
	c.JSON(http.StatusOK, gin.H{"presets": presets})
}
