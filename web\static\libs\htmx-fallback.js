/**
 * 简化版 HTMX 功能 - 基本的 AJAX 请求处理
 * 当CDN无法访问时使用此备用方案
 */

(function(global) {
    'use strict';

    var htmx = {
        // 基本配置
        config: {
            timeout: 10000,
            defaultSwapStyle: 'innerHTML'
        },

        // 发送 AJAX 请求
        ajax: function(method, url, options) {
            options = options || {};
            
            return new Promise(function(resolve, reject) {
                var xhr = new XMLHttpRequest();
                xhr.timeout = options.timeout || htmx.config.timeout;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            resolve({
                                status: xhr.status,
                                responseText: xhr.responseText,
                                xhr: xhr
                            });
                        } else {
                            reject({
                                status: xhr.status,
                                responseText: xhr.responseText,
                                xhr: xhr
                            });
                        }
                    }
                };

                xhr.onerror = function() {
                    reject({
                        status: 0,
                        responseText: 'Network Error',
                        xhr: xhr
                    });
                };

                xhr.ontimeout = function() {
                    reject({
                        status: 0,
                        responseText: 'Request Timeout',
                        xhr: xhr
                    });
                };

                xhr.open(method.toUpperCase(), url, true);
                
                // 设置请求头
                if (options.headers) {
                    for (var header in options.headers) {
                        xhr.setRequestHeader(header, options.headers[header]);
                    }
                }

                // 发送数据
                xhr.send(options.data || null);
            });
        },

        // 处理表单提交
        processForm: function(form, options) {
            options = options || {};
            
            var formData = new FormData(form);
            var method = form.method || 'POST';
            var url = form.action || window.location.href;

            return htmx.ajax(method, url, {
                data: formData,
                headers: options.headers,
                timeout: options.timeout
            });
        },

        // 更新元素内容
        swap: function(target, content, swapStyle) {
            if (!target) return;
            
            swapStyle = swapStyle || htmx.config.defaultSwapStyle;
            
            switch (swapStyle) {
                case 'innerHTML':
                    target.innerHTML = content;
                    break;
                case 'outerHTML':
                    target.outerHTML = content;
                    break;
                case 'beforeend':
                    target.insertAdjacentHTML('beforeend', content);
                    break;
                case 'afterend':
                    target.insertAdjacentHTML('afterend', content);
                    break;
                case 'beforebegin':
                    target.insertAdjacentHTML('beforebegin', content);
                    break;
                case 'afterbegin':
                    target.insertAdjacentHTML('afterbegin', content);
                    break;
                default:
                    target.innerHTML = content;
            }
        },

        // 触发事件
        trigger: function(element, eventName, detail) {
            var event;
            if (typeof CustomEvent === 'function') {
                event = new CustomEvent(eventName, {
                    detail: detail,
                    bubbles: true,
                    cancelable: true
                });
            } else {
                event = document.createEvent('CustomEvent');
                event.initCustomEvent(eventName, true, true, detail);
            }
            element.dispatchEvent(event);
        },

        // 初始化
        init: function() {
            console.log('🔄 使用简化版 HTMX 功能 (HTMX 备用方案)');
        }
    };

    // 导出到全局
    global.htmx = htmx;

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', htmx.init);
    } else {
        htmx.init();
    }

})(window);
