package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"projectm2/internal/models"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AttachmentService 附件服务
type AttachmentService struct {
	db         *gorm.DB
	uploadPath string
}

// NewAttachmentService 创建新的附件服务实例
func NewAttachmentService(db *gorm.DB) *AttachmentService {
	uploadPath := "./uploads"
	// 确保上传目录存在
	os.MkdirAll(uploadPath, 0755)

	return &AttachmentService{
		db:         db,
		uploadPath: uploadPath,
	}
}

// UploadFile 上传文件
func (s *AttachmentService) UploadFile(taskID string, file *multipart.FileHeader, uploadedBy string) (*models.Attachment, error) {
	// 验证任务是否存在（如果不是临时任务ID）
	if !strings.HasPrefix(taskID, "temp-") {
		var task models.Task
		if err := s.db.First(&task, "id = ?", taskID).Error; err != nil {
			return nil, fmt.Errorf("任务不存在")
		}
	}

	// 生成唯一文件名
	ext := filepath.Ext(file.Filename)
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 创建按日期分组的子目录
	dateDir := time.Now().Format("2006/01/02")
	fullUploadPath := filepath.Join(s.uploadPath, dateDir)
	os.MkdirAll(fullUploadPath, 0755)

	filePath := filepath.Join(fullUploadPath, fileName)

	// 打开上传的文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("无法打开上传文件: %v", err)
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("无法创建文件: %v", err)
	}
	defer dst.Close()

	// 复制文件内容
	if _, err = io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("文件保存失败: %v", err)
	}

	// 获取文件信息
	fileInfo, err := dst.Stat()
	if err != nil {
		return nil, fmt.Errorf("无法获取文件信息: %v", err)
	}

	// 确定MIME类型
	mimeType := file.Header.Get("Content-Type")
	if mimeType == "" {
		mimeType = "application/octet-stream"
	}

	// 创建附件记录
	attachment := &models.Attachment{
		TaskID:       taskID,
		FileName:     fileName,
		OriginalName: file.Filename,
		FilePath:     filePath,
		FileSize:     fileInfo.Size(),
		MimeType:     mimeType,
		Type:         models.DetermineAttachmentType(mimeType),
		UploadedBy:   uploadedBy,
	}

	// 保存到数据库
	if err := s.db.Create(attachment).Error; err != nil {
		// 如果数据库保存失败，删除已上传的文件
		os.Remove(filePath)
		return nil, fmt.Errorf("保存附件记录失败: %v", err)
	}

	return attachment, nil
}

// GetTaskAttachments 获取任务的所有附件
func (s *AttachmentService) GetTaskAttachments(taskID string) ([]models.Attachment, error) {
	var attachments []models.Attachment
	err := s.db.Where("task_id = ?", taskID).Order("created_at DESC").Find(&attachments).Error
	return attachments, err
}

// GetAttachmentByID 根据ID获取附件
func (s *AttachmentService) GetAttachmentByID(id string) (*models.Attachment, error) {
	var attachment models.Attachment
	err := s.db.First(&attachment, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &attachment, nil
}

// DeleteAttachment 删除附件
func (s *AttachmentService) DeleteAttachment(id string) error {
	attachment, err := s.GetAttachmentByID(id)
	if err != nil {
		return err
	}

	// 删除物理文件
	if err := os.Remove(attachment.FilePath); err != nil {
		// 记录错误但不阻止数据库删除
		fmt.Printf("删除文件失败: %v\n", err)
	}

	// 删除数据库记录
	return s.db.Delete(attachment).Error
}

// GetFileContent 获取文件内容（用于下载）
func (s *AttachmentService) GetFileContent(id string) (string, string, error) {
	attachment, err := s.GetAttachmentByID(id)
	if err != nil {
		return "", "", err
	}

	// 检查文件是否存在
	if _, err := os.Stat(attachment.FilePath); os.IsNotExist(err) {
		return "", "", fmt.Errorf("文件不存在")
	}

	return attachment.FilePath, attachment.OriginalName, nil
}

// ValidateFileType 验证文件类型
func (s *AttachmentService) ValidateFileType(filename string) error {
	ext := strings.ToLower(filepath.Ext(filename))

	// 允许的文件类型
	allowedTypes := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true,
		".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true,
		".ppt": true, ".pptx": true, ".txt": true, ".rtf": true,
		".zip": true, ".rar": true, ".7z": true,
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true,
		".mp3": true, ".wav": true, ".flac": true,
	}

	if !allowedTypes[ext] {
		return fmt.Errorf("不支持的文件类型: %s", ext)
	}

	return nil
}

// ValidateFileSize 验证文件大小
func (s *AttachmentService) ValidateFileSize(size int64) error {
	const maxSize = 50 * 1024 * 1024 // 50MB

	if size > maxSize {
		return fmt.Errorf("文件大小超过限制 (最大 50MB)")
	}

	return nil
}

// UpdateTempAttachmentsTaskID 更新临时附件的任务ID
func (s *AttachmentService) UpdateTempAttachmentsTaskID(tempTaskID, realTaskID string) error {
	// 更新所有临时任务ID的附件
	result := s.db.Model(&models.Attachment{}).
		Where("task_id = ?", tempTaskID).
		Update("task_id", realTaskID)

	if result.Error != nil {
		return fmt.Errorf("更新临时附件失败: %v", result.Error)
	}

	return nil
}
