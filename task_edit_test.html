<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 任务编辑数据回显测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .header h1 {
            color: #2d3748;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #718096;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }
        
        .test-section h2 {
            color: #2d3748;
            margin: 0 0 15px 0;
            font-size: 1.5rem;
        }
        
        .test-button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .result-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .result-item.success {
            background: #f0fff4;
            color: #22543d;
        }
        
        .result-item.error {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .result-item.warning {
            background: #fefcbf;
            color: #744210;
        }
        
        .status-icon {
            margin-right: 8px;
            font-weight: bold;
        }
        
        .task-data {
            background: #edf2f7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .instructions {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #234e52;
            margin: 0 0 10px 0;
        }
        
        .instructions ol {
            color: #2d3748;
            margin: 10px 0;
        }
        
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 任务编辑数据回显测试</h1>
            <p>验证任务编辑功能的数据回显是否正常工作</p>
        </div>

        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>点击下方的"测试任务编辑"按钮</li>
                <li>系统会自动打开任务编辑对话框</li>
                <li>检查以下字段是否正确回显：
                    <ul>
                        <li>✅ 分配给：应显示"王明 (wangming)"</li>
                        <li>✅ 任务类型：应显示"缺陷修复"</li>
                        <li>✅ 开始时间：应显示"2025-07-07"</li>
                        <li>✅ 结束时间：应显示"2025-07-10"</li>
                    </ul>
                </li>
                <li>如果字段没有正确显示，请查看浏览器控制台的调试信息</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🎯 测试任务数据</h2>
            <div class="task-data" id="taskData">
                正在加载任务数据...
            </div>
            <button class="test-button" onclick="testTaskEdit()">测试任务编辑</button>
            <button class="test-button success" onclick="openMainPage()">打开主页面</button>
            <button class="test-button warning" onclick="showConsoleLog()">显示控制台日志</button>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div class="test-results" id="testResults">
                <div class="result-item">
                    <span class="status-icon">ℹ️</span>
                    点击"测试任务编辑"按钮开始测试
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试任务数据（与您提供的数据一致）
        const testTaskData = {
            "id": "672d9afc-8c0c-4643-948f-cc53f616e25b",
            "title": "漏洞扫描，ssl漏洞错误",
            "description": "<p>需要漏洞修复，测试<img src=\"/api/attachments/38465844-05c6-49a0-b11c-4c947e54315d/preview\"></p>",
            "status": "in_progress",
            "priority": "medium",
            "type": "bug",
            "position": 2,
            "tags": "[]",
            "due_date": null,
            "start_date": "2025-07-07T00:00:00Z",
            "end_date": "2025-07-10T00:00:00Z",
            "estimated_hours": 0,
            "actual_hours": 0,
            "parent_task_id": null,
            "assigned_to": "fc358eea-3243-42aa-89bc-76863c833384",
            "created_by": "default-user",
            "created_at": "2025-07-02T09:24:13.7241798+08:00",
            "updated_at": "2025-07-07T08:21:50.3183751+08:00",
            "assigned_user": {
                "id": "fc358eea-3243-42aa-89bc-76863c833384",
                "username": "wangming",
                "email": "<EMAIL>",
                "full_name": "王明",
                "avatar": "",
                "role": "developer",
                "is_active": true,
                "last_login_at": null,
                "login_count": 0,
                "created_at": "2025-07-01T17:32:14.9778392+08:00",
                "updated_at": "2025-07-02T11:37:14.0663995+08:00"
            },
            "creator": {
                "id": "default-user",
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "系统管理员",
                "avatar": "",
                "role": "admin",
                "is_active": true,
                "last_login_at": "2025-07-07T09:13:04.6949028+08:00",
                "login_count": 126,
                "created_at": "2025-07-02T10:39:57.658525+08:00",
                "updated_at": "2025-07-07T09:13:04.6949028+08:00"
            }
        };

        // 显示任务数据
        function displayTaskData() {
            const taskDataElement = document.getElementById('taskData');
            taskDataElement.innerHTML = `
                <strong>任务ID:</strong> ${testTaskData.id}<br>
                <strong>标题:</strong> ${testTaskData.title}<br>
                <strong>分配给:</strong> ${testTaskData.assigned_to} (${testTaskData.assigned_user.full_name})<br>
                <strong>任务类型:</strong> ${testTaskData.type}<br>
                <strong>开始时间:</strong> ${testTaskData.start_date}<br>
                <strong>结束时间:</strong> ${testTaskData.end_date}<br>
                <strong>优先级:</strong> ${testTaskData.priority}<br>
                <strong>状态:</strong> ${testTaskData.status}
            `;
        }

        // 测试任务编辑
        function testTaskEdit() {
            addTestResult('info', '开始测试任务编辑功能...');
            
            // 模拟调用editTaskFromButton函数
            if (typeof editTaskFromButton === 'function') {
                addTestResult('success', '找到editTaskFromButton函数，开始调用');
                editTaskFromButton(testTaskData.id);
            } else {
                addTestResult('error', '未找到editTaskFromButton函数，请确保在主页面中测试');
                addTestResult('warning', '请点击"打开主页面"按钮，然后在主页面中点击任务的编辑按钮');
            }
        }

        // 打开主页面
        function openMainPage() {
            window.open('http://localhost:8086', '_blank');
        }

        // 显示控制台日志
        function showConsoleLog() {
            addTestResult('info', '请打开浏览器开发者工具查看控制台日志');
            addTestResult('info', '按F12打开开发者工具，然后查看Console标签页');
        }

        // 添加测试结果
        function addTestResult(type, message) {
            const resultsElement = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            resultItem.innerHTML = `
                <span class="status-icon">${icon}</span>
                ${message}
            `;
            
            resultsElement.appendChild(resultItem);
            
            // 滚动到最新结果
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载时显示任务数据
        document.addEventListener('DOMContentLoaded', function() {
            displayTaskData();
            addTestResult('info', '测试页面已加载，任务数据已显示');
        });
    </script>
</body>
</html>
