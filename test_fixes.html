<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🔧 项目管理系统功能修复测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>本页面用于测试以下修复的功能：</p>
        <ul>
            <li><strong>富文本编辑器工具栏显示</strong> - 确保所有格式化按钮正常显示</li>
            <li><strong>快捷时间选择功能</strong> - 确保时间预设按钮正常加载和工作</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🌐 API连接测试</h2>
        <button class="test-button" onclick="testAPI()">测试API连接</button>
        <button class="test-button" onclick="testTimePresets()">测试时间预设API</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>🎯 功能测试步骤</h2>
        <ol>
            <li>
                <strong>富文本编辑器测试：</strong>
                <ul>
                    <li>点击主页的"新建任务"按钮</li>
                    <li>查看任务描述字段的富文本编辑器</li>
                    <li>验证工具栏是否完整显示（粗体、斜体、列表、图片、附件等按钮）</li>
                    <li>测试各个格式化按钮是否正常工作</li>
                </ul>
            </li>
            <li>
                <strong>时间预设测试：</strong>
                <ul>
                    <li>在新建任务对话框中，找到"快捷时间选择"区域</li>
                    <li>验证是否显示时间预设按钮（今天、1天、3天、1周等）</li>
                    <li>点击任意时间预设按钮，验证开始和结束时间是否自动填充</li>
                    <li>验证是否有视觉反馈（蓝色边框闪烁）</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔗 快速链接</h2>
        <a href="http://localhost:8086" target="_blank" class="test-button">打开主应用</a>
        <button class="test-button" onclick="openNewTaskModal()">直接测试新建任务</button>
    </div>

    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="test-results">
            <p class="info">请按照上述步骤进行测试，并记录结果。</p>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('api-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8086/api/tasks');
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ API连接正常，任务数: ${data.tasks ? data.tasks.length : 0}`, 'success');
                } else {
                    showResult(`❌ API响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ API连接失败: ${error.message}`, 'error');
            }
        }

        async function testTimePresets() {
            try {
                const response = await fetch('http://localhost:8086/api/time/presets');
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ 时间预设API正常，预设数: ${data.presets ? data.presets.length : 0}`, 'success');
                    if (data.presets && data.presets.length > 0) {
                        showResult(`📋 预设列表: ${data.presets.map(p => p.name).join(', ')}`, 'info');
                    }
                } else {
                    showResult(`❌ 时间预设API错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 时间预设API失败: ${error.message}`, 'error');
            }
        }

        function openNewTaskModal() {
            window.open('http://localhost:8086', '_blank');
            showResult('💡 请在新打开的页面中点击"新建任务"按钮进行测试', 'info');
        }

        // 页面加载时自动测试API
        window.addEventListener('load', function() {
            setTimeout(() => {
                testAPI();
                testTimePresets();
            }, 1000);
        });
    </script>
</body>
</html>
