/* 现代化项目管理系统 - 国际一流产品设计 */

/* 🎨 现代化设计系统 - Design System */
:root {
    /* 🌈 主色彩系统 - Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* 🎯 语义化色彩 - Semantic Colors */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;

    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;

    /* 🌫️ 中性色系统 - Neutral Colors */
    --neutral-0: #ffffff;
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --neutral-950: #0a0a0a;

    /* 🎭 渐变色系统 - Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    --gradient-danger: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* 🌟 阴影系统 - Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

    /* 📐 间距系统 - Spacing System */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */

    /* 🔄 圆角系统 - Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;

    /* ⚡ 动画系统 - Animation System */
    --duration-75: 75ms;
    --duration-100: 100ms;
    --duration-150: 150ms;
    --duration-200: 200ms;
    --duration-300: 300ms;
    --duration-500: 500ms;
    --duration-700: 700ms;
    --duration-1000: 1000ms;

    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* 📱 断点系统 - Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* 🌙 暗色主题系统 - Dark Theme */
[data-theme="dark"] {
    /* 重新映射中性色 */
    --neutral-0: #0a0a0a;
    --neutral-50: #171717;
    --neutral-100: #262626;
    --neutral-200: #404040;
    --neutral-300: #525252;
    --neutral-400: #737373;
    --neutral-500: #a3a3a3;
    --neutral-600: #d4d4d4;
    --neutral-700: #e5e5e5;
    --neutral-800: #f5f5f5;
    --neutral-900: #fafafa;
    --neutral-950: #ffffff;

    /* 暗色主题渐变 */
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.2);
}

/* 🎯 全局基础样式 - Global Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*,
*::before,
*::after {
    transition: all var(--duration-200) var(--ease-out);
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--neutral-50);
    color: var(--neutral-900);
    font-weight: 400;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 🎪 拖拽交互系统 - Drag & Drop System */
.sortable-ghost {
    opacity: 0.3;
    transform: rotate(3deg) scale(0.98);
    background: var(--gradient-glass);
    backdrop-filter: blur(8px);
    border: 2px dashed var(--primary-300);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: all var(--duration-300) var(--ease-bounce);
}

.sortable-chosen {
    transform: rotate(1deg) scale(1.03);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    z-index: 1000;
    border-radius: var(--radius-xl);
    background: var(--neutral-0);
    transition: all var(--duration-200) var(--ease-out);
}

.sortable-drag {
    transform: rotate(2deg) scale(1.05);
    opacity: 0.9;
    box-shadow: var(--shadow-2xl);
    filter: brightness(1.1);
    transition: all var(--duration-150) var(--ease-out);
}

/* 🎴 任务卡片系统 - Task Card System */
.task-card {
    position: relative;
    background: var(--neutral-0);
    border: 1px solid var(--neutral-100);
    border-radius: var(--radius-xl);
    padding: 0.75rem;
    cursor: pointer;
    overflow: hidden;
    transition: all var(--duration-300) cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin-bottom: 0.5rem;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: translateX(-100%);
    transition: transform var(--duration-500) var(--ease-out);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.task-card::after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity var(--duration-300) var(--ease-out);
    pointer-events: none;
    border-radius: var(--radius-xl);
}

.task-card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 20px rgba(99, 102, 241, 0.15);
    border-color: var(--primary-200);
}

.task-card:hover::before {
    transform: translateX(100%);
}

.task-card:hover::after {
    opacity: 1;
}

.task-card:active {
    transform: translateY(-2px) scale(1.01);
    transition: all var(--duration-100) var(--ease-out);
}

/* 🎯 优先级指示系统 - Priority Indicators */
.priority-high {
    border-left: 4px solid var(--danger-500);
    background: linear-gradient(135deg, var(--neutral-0) 0%, var(--danger-50) 100%);
}

.priority-high::before {
    background: var(--gradient-danger);
}

.priority-high:hover {
    border-color: var(--danger-400);
    box-shadow: var(--shadow-2xl), 0 0 20px rgba(239, 68, 68, 0.3);
}

.priority-medium {
    border-left: 4px solid var(--warning-500);
    background: linear-gradient(135deg, var(--neutral-0) 0%, var(--warning-50) 100%);
}

.priority-medium::before {
    background: var(--gradient-warning);
}

.priority-medium:hover {
    border-color: var(--warning-400);
    box-shadow: var(--shadow-2xl), 0 0 20px rgba(245, 158, 11, 0.3);
}

.priority-low {
    border-left: 4px solid var(--success-500);
    background: linear-gradient(135deg, var(--neutral-0) 0%, var(--success-50) 100%);
}

.priority-low::before {
    background: var(--gradient-success);
}

.priority-low:hover {
    border-color: var(--success-400);
    box-shadow: var(--shadow-2xl), 0 0 20px rgba(16, 185, 129, 0.3);
}

/* 🏛️ 列头系统 - Column Header System */
.column-header {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    padding: var(--space-5) var(--space-6);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}

/* 待办列 - 精致蓝紫渐变 */
.column-header.todo {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
}

/* 进行中列 - 活力橙红渐变 */
.column-header.in-progress {
    background: linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #ef4444 100%);
}

/* 已完成列 - 清新绿色渐变 */
.column-header.done {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

/* 默认渐变（向后兼容） */
.column-header:not(.todo):not(.in-progress):not(.done) {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.column-header::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-glass);
    opacity: 0.5;
    transition: opacity var(--duration-300) var(--ease-out);
}

.column-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left var(--duration-700) var(--ease-out);
}

.column-header:hover::before {
    opacity: 0.8;
}

.column-header:hover::after {
    left: 100%;
}

.column-header h2 {
    position: relative;
    z-index: 2;
    color: var(--neutral-0);
    font-weight: 600;
    font-size: 1.125rem;
    letter-spacing: -0.025em;
}

/* 🔘 按钮系统 - Button System */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--neutral-0);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: -0.01em;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all var(--duration-200) var(--ease-out);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

.btn-primary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity var(--duration-200) var(--ease-out);
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left var(--duration-500) var(--ease-out);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-primary:hover::before {
    opacity: 1;
}

.btn-primary:hover::after {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(-1px) scale(1.01);
    transition: all var(--duration-100) var(--ease-out);
}

.btn-primary:focus {
    outline: none;
    box-shadow: var(--shadow-xl), 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 🪟 模态框系统 - Modal System */
.modal-backdrop {
    backdrop-filter: blur(12px) saturate(1.2);
    background: rgba(0, 0, 0, 0.4);
    transition: all var(--duration-300) var(--ease-out);
}

.modal-content {
    background: var(--neutral-0);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--neutral-200);
    backdrop-filter: blur(20px);
    animation: modalSlideIn var(--duration-500) var(--ease-bounce);
    overflow: hidden;
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-60px) scale(0.9) rotate(1deg);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-10px) scale(1.02) rotate(-0.5deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotate(0deg);
    }
}

@keyframes modalSlideOut {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.9) rotate(-1deg);
    }
}

.modal-content.closing {
    animation: modalSlideOut var(--duration-300) var(--ease-in);
}

/* 📝 表单系统 - Form System */
.form-input {
    background: var(--neutral-0);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    color: var(--neutral-900);
    transition: all var(--duration-200) var(--ease-out);
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: var(--neutral-400);
    font-weight: 400;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md);
    transform: translateY(-2px) scale(1.01);
    background: var(--neutral-0);
}

.form-input:hover:not(:focus) {
    border-color: var(--neutral-300);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--neutral-700);
    margin-bottom: var(--space-2);
    display: block;
    letter-spacing: -0.01em;
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-error {
    color: var(--danger-600);
    font-size: 0.75rem;
    margin-top: var(--space-1);
    font-weight: 500;
}

.form-success {
    color: var(--success-600);
    font-size: 0.75rem;
    margin-top: var(--space-1);
    font-weight: 500;
}

/* 🎨 任务卡片内容样式 - Task Card Content */
.task-title {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--neutral-900);
    line-height: 1.4;
    letter-spacing: -0.025em;
    cursor: pointer;
    transition: all var(--duration-300) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin-bottom: var(--space-2);
}

.task-title:hover {
    color: var(--primary-600);
    transform: translateX(3px);
}

.task-description {
    font-size: 0.9rem;
    color: var(--neutral-600);
    line-height: 1.6;
    margin: var(--space-2) 0 var(--space-3) 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 400;
    text-overflow: ellipsis;
}

.task-meta {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: var(--neutral-500);
    margin: var(--space-2) 0;
    gap: var(--space-2);
    font-weight: 500;
}

.task-meta svg {
    width: 0.875rem;
    height: 0.875rem;
    opacity: 0.8;
    color: var(--neutral-400);
}

.task-actions {
    display: flex;
    gap: var(--space-1);
    opacity: 0;
    transition: all var(--duration-200) var(--ease-out);
}

.task-card:hover .task-actions {
    opacity: 1;
}

.task-action-btn {
    padding: var(--space-1);
    border-radius: var(--radius-md);
    color: var(--neutral-400);
    transition: all var(--duration-200) var(--ease-out);
    cursor: pointer;
    background: transparent;
    border: none;
}

.task-action-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
    transform: scale(1.1);
}

.task-action-btn.delete:hover {
    background: var(--danger-50);
    color: var(--danger-600);
}

/* 🏷️ 优先级标签系统 - Priority Badge System */
.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    transition: all var(--duration-200) var(--ease-out);
}

.priority-badge.high {
    background: var(--danger-100);
    color: var(--danger-700);
    border: 1px solid var(--danger-200);
}

.priority-badge.medium {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

.priority-badge.low {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.priority-badge:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-sm);
}

/* 🏷️ 任务类型图标系统 - Task Type Icon System */
.task-type-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--neutral-200);
    font-size: 1rem;
    transition: all var(--duration-200) var(--ease-out);
    box-shadow: var(--shadow-xs);
}

.task-type-icon:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-color: var(--primary-200);
}

/* 🏷️ 任务类型标签样式 - Task Type Badge */
.task-type-name {
    font-weight: 500;
    letter-spacing: 0.025em;
    white-space: nowrap;
    transition: all var(--duration-200) var(--ease-out);
}

/* 📋 用户信息标签样式 - User Info Badge */
.user-info-badge {
    transition: all var(--duration-200) var(--ease-out);
    backdrop-filter: blur(8px);
}

.user-info-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 🎯 任务卡片内容优化 - Task Card Content */
.task-title {
    font-weight: 600;
    line-height: 1.3;
    color: var(--neutral-800);
    transition: color var(--duration-200) var(--ease-out);
}

.task-description {
    line-height: 1.4;
    color: var(--neutral-600);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 🎨 紧凑布局优化 - Compact Layout */
.task-action-btn {
    padding: 0.25rem;
    border-radius: 0.375rem;
    transition: all var(--duration-200) var(--ease-out);
    opacity: 0.6;
}

.task-action-btn:hover {
    opacity: 1;
    background-color: var(--neutral-100);
}

.task-actions {
    display: flex;
    gap: 0.25rem;
}

.priority-badge {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

/* 🎨 图标选择器样式 - Icon Picker Styles */
#iconPicker {
    position: relative;
    z-index: 10000;
    animation: fadeInUp 0.2s ease-out;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.icon-category-btn {
    transition: all var(--duration-200) var(--ease-out);
    font-weight: 500;
}

.icon-category-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#iconGrid button {
    transition: all var(--duration-200) var(--ease-out);
    position: relative;
}

#iconGrid button:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

#iconGrid button:active {
    transform: scale(0.95);
}

/* 🕒 时间进度指示器 - Time Progress Indicator */
.time-progress {
    position: relative;
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: var(--space-2) 0;
}

.time-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-500) 0%, var(--warning-500) 50%, var(--danger-500) 100%);
    border-radius: var(--radius-full);
    transition: width var(--duration-500) var(--ease-out);
    position: relative;
}

.time-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.time-indicator {
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    color: var(--neutral-500);
    gap: var(--space-1);
}

.time-indicator.urgent {
    color: var(--danger-600);
    font-weight: 600;
}

.time-indicator.warning {
    color: var(--warning-600);
    font-weight: 500;
}

.time-indicator.normal {
    color: var(--success-600);
}

/* 🎯 已完成任务样式 - Completed Task Styles */
.task-card[data-status="done"] {
    opacity: 0.8;
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--success-50) 100%);
    border-color: var(--success-200);
}

.task-card[data-status="done"] .task-title {
    text-decoration: line-through;
    color: var(--neutral-600);
}

.task-card[data-status="done"] .task-description {
    color: var(--neutral-500);
}

.task-card[data-status="done"]:hover {
    opacity: 1;
    transform: translateY(-2px) scale(1.005);
}

/* ✨ 微交互动画系统 - Micro Interactions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
}

/* 🎪 动画工具类 - Animation Utilities */
.animate-fade-in {
    animation: fadeIn var(--duration-300) var(--ease-out);
}

.animate-slide-in-right {
    animation: slideInRight var(--duration-300) var(--ease-out);
}

.animate-pulse {
    animation: pulse var(--duration-1000) infinite;
}

.animate-bounce {
    animation: bounce var(--duration-1000) infinite;
}

.animate-shake {
    animation: shake var(--duration-500) var(--ease-out);
}

.animate-glow {
    animation: glow var(--duration-1000) infinite;
}

/* 🌊 加载动画系统 - Loading Animations */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--neutral-200);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin var(--duration-1000) linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: var(--space-1);
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background: var(--primary-500);
    border-radius: 50%;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 📱 响应式设计系统 - Responsive Design */
@media (max-width: 768px) {
    /* 移动端任务卡片优化 */
    .task-card {
        padding: var(--space-3);
        margin-bottom: var(--space-3);
    }

    .task-title {
        font-size: 0.875rem;
        line-height: 1.3;
    }

    .task-description {
        font-size: 0.8rem;
        -webkit-line-clamp: 1;
    }

    .task-actions {
        opacity: 1; /* 移动端始终显示操作按钮 */
    }

    .task-action-btn {
        padding: var(--space-2);
    }

    /* 列头优化 */
    .column-header {
        padding: var(--space-3) var(--space-4);
    }

    .column-header h2 {
        font-size: 1rem;
    }

    /* 按钮优化 */
    .btn-primary {
        padding: var(--space-2) var(--space-4);
        font-size: 0.8rem;
    }

    /* 模态框优化 */
    .modal-content {
        margin: var(--space-4);
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    /* 表单优化 */
    .form-input {
        padding: var(--space-3);
        font-size: 1rem; /* 防止iOS缩放 */
    }

    /* 优先级标签优化 */
    .priority-badge {
        font-size: 0.7rem;
        padding: var(--space-1) var(--space-2);
    }
}

@media (max-width: 640px) {
    /* 超小屏幕优化 */
    .task-card {
        padding: var(--space-2);
    }

    .task-title {
        font-size: 0.8rem;
    }

    .task-meta {
        font-size: 0.7rem;
    }

    .column-header {
        padding: var(--space-2) var(--space-3);
    }

    .column-header h2 {
        font-size: 0.875rem;
    }
}

/* 🖥️ 大屏幕优化 - Large Screen Optimization */
@media (min-width: 1280px) {
    .task-card {
        padding: var(--space-5);
    }

    .task-title {
        font-size: 1.125rem;
    }

    .task-description {
        font-size: 1rem;
        -webkit-line-clamp: 3;
    }

    .column-header {
        padding: var(--space-5) var(--space-6);
    }

    .column-header h2 {
        font-size: 1.25rem;
    }
}

/* 🎨 暗色主题适配 - Dark Theme Adaptations */
[data-theme="dark"] .task-card {
    background: var(--neutral-100);
    border-color: var(--neutral-300);
    color: var(--neutral-900);
}

[data-theme="dark"] .task-card:hover {
    border-color: var(--primary-400);
    background: var(--neutral-50);
}

[data-theme="dark"] .task-title {
    color: var(--neutral-900);
}

[data-theme="dark"] .task-title:hover {
    color: var(--primary-400);
}

[data-theme="dark"] .task-description {
    color: var(--neutral-600);
}

[data-theme="dark"] .task-action-btn {
    color: var(--neutral-500);
}

[data-theme="dark"] .task-action-btn:hover {
    background: var(--neutral-200);
    color: var(--primary-400);
}

[data-theme="dark"] .task-action-btn.delete:hover {
    background: var(--danger-100);
    color: var(--danger-400);
}

[data-theme="dark"] .priority-badge.high {
    background: var(--danger-200);
    color: var(--danger-100);
    border-color: var(--danger-300);
}

[data-theme="dark"] .priority-badge.medium {
    background: var(--warning-200);
    color: var(--warning-100);
    border-color: var(--warning-300);
}

[data-theme="dark"] .priority-badge.low {
    background: var(--success-200);
    color: var(--success-100);
    border-color: var(--success-300);
}

[data-theme="dark"] body {
    background: var(--neutral-50);
    color: var(--neutral-900);
}

/* 🎯 高级功能样式 - Advanced Features Styles */

/* 通知中心样式 */
.notification-item {
    transition: all var(--duration-200) var(--ease-out);
}

.notification-item:hover {
    transform: translateX(-2px);
    box-shadow: var(--shadow-md);
}

/* 图表容器样式 */
.chart-content {
    transition: all var(--duration-300) var(--ease-out);
}

.chart-tab {
    transition: all var(--duration-200) var(--ease-out);
    border-bottom: 2px solid transparent;
}

.chart-tab:hover {
    color: var(--primary-600) !important;
    border-bottom-color: var(--primary-300);
}

.chart-tab.active {
    color: var(--primary-600) !important;
    border-bottom-color: var(--primary-500);
    font-weight: 600;
}

/* 用户在线状态指示器 */
.user-status-indicator {
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    border: 2px solid var(--neutral-0);
    border-radius: 50%;
}

.user-status-indicator.active {
    background: var(--success-500);
    animation: pulse var(--duration-1000) infinite;
}

.user-status-indicator.idle {
    background: var(--warning-500);
}

.user-status-indicator.offline {
    background: var(--neutral-400);
}

/* 协作功能样式 */
.collaboration-panel {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--neutral-200);
}

[data-theme="dark"] .collaboration-panel {
    background: rgba(38, 38, 38, 0.95);
    border-color: var(--neutral-300);
}

/* 导出选项样式 */
.export-option {
    transition: all var(--duration-200) var(--ease-out);
    border: 2px solid var(--neutral-200);
}

.export-option:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-option.selected {
    border-color: var(--primary-500);
    background: var(--primary-100);
}

/* 备份历史样式 */
.backup-record {
    transition: all var(--duration-200) var(--ease-out);
}

.backup-record:hover {
    background: var(--neutral-50);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

/* 数据洞察卡片样式 */
.insight-card {
    background: linear-gradient(135deg, var(--neutral-0) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--neutral-200);
    transition: all var(--duration-300) var(--ease-out);
}

.insight-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.insight-card.blue {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-color: var(--primary-200);
}

.insight-card.green {
    background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
    border-color: var(--success-200);
}

.insight-card.purple {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    border-color: #d8b4fe;
}

/* 高级搜索样式 */
.advanced-search-field {
    transition: all var(--duration-200) var(--ease-out);
}

.advanced-search-field:focus-within {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

/* 模板选择器样式 */
.template-option {
    transition: all var(--duration-200) var(--ease-out);
    border: 1px solid var(--neutral-200);
}

.template-option:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 评论系统样式 */
.comment-bubble {
    position: relative;
    background: var(--neutral-100);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    margin-bottom: var(--space-2);
}

.comment-bubble.own {
    background: var(--primary-100);
    margin-left: var(--space-8);
}

.comment-bubble.other {
    background: var(--neutral-100);
    margin-right: var(--space-8);
}

.comment-bubble::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 16px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--neutral-100);
}

.comment-bubble.own::before {
    border-top-color: var(--primary-100);
    left: auto;
    right: 16px;
}

/* 状态指示器样式 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.online {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.status-indicator.idle {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

.status-indicator.offline {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-200);
}

/* 进度环样式 */
.progress-ring {
    transform: rotate(-90deg);
    transition: all var(--duration-500) var(--ease-out);
}

.progress-ring circle {
    transition: stroke-dashoffset var(--duration-500) var(--ease-out);
}

/* 数据可视化增强 */
.chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    color: var(--neutral-700);
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* 动画增强 */
@keyframes chartBarGrow {
    from {
        height: 0;
    }
    to {
        height: var(--bar-height);
    }
}

.chart-bar {
    animation: chartBarGrow var(--duration-700) var(--ease-out);
}

@keyframes pieSliceGrow {
    from {
        stroke-dasharray: 0 251;
    }
    to {
        stroke-dasharray: var(--slice-length) 251;
    }
}

.pie-slice {
    animation: pieSliceGrow var(--duration-700) var(--ease-out);
}

/* 响应式增强 */
@media (max-width: 768px) {
    .collaboration-panel,
    .notification-center {
        width: 100vw;
        height: 100vh;
        top: 0;
        right: 0;
        border-radius: 0;
    }

    .chart-container {
        padding: var(--space-2);
    }

    .insight-card {
        padding: var(--space-3);
    }

    .chart-legend {
        flex-direction: column;
        align-items: center;
        gap: var(--space-2);
    }
}

/* 🔐 企业级功能样式 - Enterprise Features Styles */

/* 权限管理样式 */
.permission-card {
    transition: all var(--duration-200) var(--ease-out);
    border: 2px solid var(--neutral-200);
}

.permission-card.current {
    border-color: var(--primary-500);
    background: var(--primary-50);
    box-shadow: var(--shadow-md);
}

.permission-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.permission-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all var(--duration-200) var(--ease-out);
}

.permission-indicator.granted {
    background: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.permission-indicator.denied {
    background: var(--neutral-300);
}

/* 审计日志样式 */
.audit-log-entry {
    transition: all var(--duration-200) var(--ease-out);
    border-left: 4px solid transparent;
}

.audit-log-entry:hover {
    border-left-color: var(--primary-500);
    background: var(--primary-25);
    transform: translateX(4px);
}

.audit-log-entry.critical {
    border-left-color: var(--danger-500);
    background: var(--danger-25);
}

.audit-log-entry.warning {
    border-left-color: var(--warning-500);
    background: var(--warning-25);
}

/* API监控样式 */
.api-status-indicator {
    position: relative;
    display: inline-block;
}

.api-status-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    animation: pulse var(--duration-1000) infinite;
}

.api-status-indicator.healthy::after {
    background: rgba(16, 185, 129, 0.3);
}

.api-status-indicator.warning::after {
    background: rgba(245, 158, 11, 0.3);
}

.api-status-indicator.error::after {
    background: rgba(239, 68, 68, 0.3);
}

.api-request-row {
    transition: all var(--duration-200) var(--ease-out);
}

.api-request-row:hover {
    background: var(--neutral-50);
    transform: scale(1.01);
}

.api-request-row.success {
    border-left: 3px solid var(--success-500);
}

.api-request-row.error {
    border-left: 3px solid var(--danger-500);
}

/* 插件系统样式 */
.plugin-card {
    transition: all var(--duration-300) var(--ease-out);
    border: 1px solid var(--neutral-200);
    position: relative;
    overflow: hidden;
}

.plugin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left var(--duration-500) var(--ease-out);
}

.plugin-card:hover::before {
    left: 100%;
}

.plugin-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.plugin-card.enabled {
    border-color: var(--success-300);
    background: var(--success-25);
}

.plugin-card.disabled {
    opacity: 0.7;
    background: var(--neutral-25);
}

.plugin-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.plugin-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.plugin-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--neutral-300);
    transition: var(--duration-300);
    border-radius: 24px;
}

.plugin-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--duration-300);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.plugin-toggle input:checked + .plugin-toggle-slider {
    background-color: var(--success-500);
}

.plugin-toggle input:checked + .plugin-toggle-slider:before {
    transform: translateX(20px);
}

/* 统计小部件样式 */
.stats-widget {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--neutral-200);
    transition: all var(--duration-300) var(--ease-out);
}

.stats-widget:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.stats-number {
    font-variant-numeric: tabular-nums;
    transition: all var(--duration-300) var(--ease-out);
}

.stats-number.updated {
    animation: numberUpdate var(--duration-500) var(--ease-out);
}

@keyframes numberUpdate {
    0% {
        transform: scale(1);
        color: inherit;
    }
    50% {
        transform: scale(1.2);
        color: var(--primary-600);
    }
    100% {
        transform: scale(1);
        color: inherit;
    }
}

/* 快速笔记样式 */
.quick-notes-widget {
    backdrop-filter: blur(10px);
    background: rgba(254, 243, 199, 0.9);
    border: 1px solid var(--warning-200);
    transition: all var(--duration-300) var(--ease-out);
}

.quick-notes-widget.collapsed .notes-content {
    display: none;
}

.quick-notes-widget:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 角色标签样式 */
.role-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-badge.admin {
    background: var(--danger-100);
    color: var(--danger-700);
    border: 1px solid var(--danger-200);
}

.role-badge.manager {
    background: var(--primary-100);
    color: var(--primary-700);
    border: 1px solid var(--primary-200);
}

.role-badge.member {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.role-badge.viewer {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-200);
}

/* 企业级表格样式 */
.enterprise-table {
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.enterprise-table th {
    background: var(--neutral-50);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    color: var(--neutral-700);
    border-bottom: 1px solid var(--neutral-200);
}

.enterprise-table td {
    border-bottom: 1px solid var(--neutral-100);
}

.enterprise-table tr:last-child td {
    border-bottom: none;
}

.enterprise-table tr:hover {
    background: var(--neutral-25);
}

/* 代码块样式 */
.code-block {
    background: var(--neutral-900);
    color: var(--neutral-100);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
}

.code-inline {
    background: var(--neutral-100);
    color: var(--neutral-800);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
}

/* 暗色主题适配 */
[data-theme="dark"] .stats-widget {
    background: rgba(38, 38, 38, 0.9);
    border-color: var(--neutral-300);
}

[data-theme="dark"] .quick-notes-widget {
    background: rgba(68, 64, 60, 0.9);
    border-color: var(--warning-300);
}

[data-theme="dark"] .enterprise-table th {
    background: var(--neutral-200);
    color: var(--neutral-800);
}

[data-theme="dark"] .enterprise-table tr:hover {
    background: var(--neutral-100);
}

[data-theme="dark"] .code-inline {
    background: var(--neutral-200);
    color: var(--neutral-800);
}

/* 🌍 国际化与可访问性样式 - I18n & Accessibility Styles */

/* 语言选择器样式 */
.language-selector {
    position: relative;
}

.language-dropdown {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-xl);
}

[data-theme="dark"] .language-dropdown {
    background: rgba(38, 38, 38, 0.95);
    border-color: var(--neutral-300);
}

.language-option {
    transition: all var(--duration-200) var(--ease-out);
}

.language-option:hover {
    background: var(--primary-50);
    transform: translateX(4px);
}

.language-option.selected {
    background: var(--primary-100);
    color: var(--primary-700);
    font-weight: 600;
}

/* 可访问性 - 键盘焦点指示器 */
.keyboard-focus {
    outline: 3px solid var(--primary-500) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* 屏幕阅读器专用内容 */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* 高对比度模式 */
.high-contrast {
    --primary-500: #000000;
    --primary-600: #000000;
    --primary-700: #000000;
    --neutral-0: #ffffff;
    --neutral-50: #f8f8f8;
    --neutral-100: #e0e0e0;
    --neutral-200: #c0c0c0;
    --neutral-300: #a0a0a0;
    --neutral-400: #808080;
    --neutral-500: #606060;
    --neutral-600: #404040;
    --neutral-700: #202020;
    --neutral-800: #101010;
    --neutral-900: #000000;
    --success-500: #008000;
    --warning-500: #ff8000;
    --danger-500: #ff0000;
}

.high-contrast * {
    border-color: var(--neutral-900) !important;
}

.high-contrast .task-card {
    border: 2px solid var(--neutral-900) !important;
    background: var(--neutral-0) !important;
    color: var(--neutral-900) !important;
}

.high-contrast button {
    border: 2px solid var(--neutral-900) !important;
    background: var(--neutral-0) !important;
    color: var(--neutral-900) !important;
}

.high-contrast button:hover {
    background: var(--neutral-100) !important;
}

.high-contrast .btn-primary {
    background: var(--neutral-900) !important;
    color: var(--neutral-0) !important;
}

/* 减少动画模式 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

/* 键盘导航增强 */
.task-card:focus {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
    z-index: 10;
}

.task-card[aria-selected="true"] {
    background: var(--primary-50);
    border-color: var(--primary-500);
}

/* 焦点陷阱样式 */
.modal.focus-trap {
    position: relative;
}

.modal.focus-trap::before,
.modal.focus-trap::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
}

/* 跳转链接 */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-600);
    color: var(--neutral-0);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 1000;
    transition: top var(--duration-200) var(--ease-out);
}

.skip-link:focus {
    top: 6px;
}

/* ARIA状态指示器 */
[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

[aria-selected="true"] {
    background: var(--primary-100);
    color: var(--primary-700);
}

[aria-pressed="true"] {
    background: var(--primary-600);
    color: var(--neutral-0);
}

/* 键盘快捷键显示 */
kbd {
    background: var(--neutral-100);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-sm);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2), 0 2px 0 0 rgba(255, 255, 255, 0.7) inset;
    color: var(--neutral-700);
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1;
    padding: 2px 4px;
    white-space: nowrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

[data-theme="dark"] kbd {
    background: var(--neutral-200);
    border-color: var(--neutral-400);
    color: var(--neutral-800);
}

/* 语音提示样式 */
.voice-command-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--primary-600);
    color: var(--neutral-0);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    animation: pulse var(--duration-1000) infinite;
}

/* 触摸友好的交互区域 */
@media (pointer: coarse) {
    button,
    .task-card,
    .clickable {
        min-height: 44px;
        min-width: 44px;
    }

    .task-card {
        padding: var(--space-4);
    }

    .btn-sm {
        min-height: 36px;
        padding: var(--space-2) var(--space-3);
    }
}

/* 打印样式 */
@media print {
    .no-print,
    header,
    .floating-action-btn,
    .toast,
    .modal {
        display: none !important;
    }

    .task-card {
        break-inside: avoid;
        border: 1px solid #000;
        margin-bottom: var(--space-2);
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* 色盲友好的颜色指示器 */
.colorblind-friendly .priority-high::before {
    content: '🔴 ';
}

.colorblind-friendly .priority-medium::before {
    content: '🟡 ';
}

.colorblind-friendly .priority-low::before {
    content: '🟢 ';
}

.colorblind-friendly .status-todo::before {
    content: '📋 ';
}

.colorblind-friendly .status-progress::before {
    content: '⚡ ';
}

.colorblind-friendly .status-done::before {
    content: '✅ ';
}

/* 动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 对比度偏好设置 */
@media (prefers-contrast: high) {
    :root {
        --primary-500: #000000;
        --neutral-0: #ffffff;
        --neutral-900: #000000;
    }

    .task-card {
        border: 2px solid var(--neutral-900);
    }

    button {
        border: 2px solid var(--neutral-900);
    }
}

/* 颜色方案偏好设置 */
@media (prefers-color-scheme: dark) {
    :root {
        --neutral-0: #1a1a1a;
        --neutral-50: #262626;
        --neutral-100: #404040;
        --neutral-900: #ffffff;
    }
}

/* 透明度偏好设置 */
@media (prefers-reduced-transparency: reduce) {
    .backdrop-blur,
    .glass-effect {
        backdrop-filter: none !important;
        background: var(--neutral-0) !important;
    }
}

/* 响应式可访问性增强 */
@media (max-width: 768px) {
    .keyboard-focus {
        outline-width: 2px;
        outline-offset: 1px;
    }

    .task-card:focus {
        outline-width: 2px;
        outline-offset: 1px;
    }

    kbd {
        font-size: 0.875rem;
        padding: 4px 6px;
    }
}

/* 语言特定样式 */
[lang="ja"] {
    font-family: 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
    line-height: 1.7;
}

[lang="zh-CN"] {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
}

[lang="en"] {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.5;
}

/* RTL语言支持 */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .task-card {
    text-align: right;
}

[dir="rtl"] .flex {
    flex-direction: row-reverse;
}

/* 可访问性工具栏 */
.accessibility-toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--neutral-900);
    color: var(--neutral-0);
    padding: var(--space-2);
    z-index: 1000;
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    transform: translateY(-100%);
    transition: transform var(--duration-300) var(--ease-out);
}

.accessibility-toolbar.visible {
    transform: translateY(0);
}

.accessibility-toolbar button {
    background: transparent;
    border: 1px solid var(--neutral-0);
    color: var(--neutral-0);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
}

.accessibility-toolbar button:hover {
    background: var(--neutral-0);
    color: var(--neutral-900);
}

/* 🎪 高级微交互系统 - Advanced Micro Interactions */

/* 任务卡片高级动画 */
.task-card {
    transform-origin: center;
    will-change: transform, box-shadow;
}

.task-card:hover {
    animation: cardHover var(--duration-300) var(--ease-out) forwards;
}

@keyframes cardHover {
    0% {
        transform: translateY(0) scale(1);
        box-shadow: var(--shadow-md);
    }
    50% {
        transform: translateY(-3px) scale(1.01);
        box-shadow: var(--shadow-lg);
    }
    100% {
        transform: translateY(-6px) scale(1.02);
        box-shadow: var(--shadow-2xl), var(--shadow-glow);
    }
}

/* 按钮波纹效果 */
.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width var(--duration-300) var(--ease-out), height var(--duration-300) var(--ease-out);
}

.btn-primary:active::after {
    width: 300px;
    height: 300px;
}

/* 输入框聚焦动画 */
.form-input {
    position: relative;
}

.form-input:focus {
    animation: inputFocus var(--duration-300) var(--ease-out);
}

@keyframes inputFocus {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    100% {
        transform: scale(1.01);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md);
    }
}

/* 优先级标签动画 */
.priority-badge {
    position: relative;
    overflow: hidden;
}

.priority-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left var(--duration-500) var(--ease-out);
}

.priority-badge:hover::before {
    left: 100%;
}

/* 任务操作按钮动画 */
.task-action-btn {
    position: relative;
    overflow: hidden;
}

.task-action-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: currentColor;
    opacity: 0;
    border-radius: inherit;
    transition: opacity var(--duration-200) var(--ease-out);
}

.task-action-btn:hover::before {
    opacity: 0.1;
}

.task-action-btn:active {
    animation: buttonPress var(--duration-150) var(--ease-out);
}

@keyframes buttonPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* 列头闪光效果 */
.column-header {
    position: relative;
    overflow: hidden;
}

.column-header:hover {
    animation: headerGlow var(--duration-700) var(--ease-out);
}

@keyframes headerGlow {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.1);
    }
    100% {
        filter: brightness(1);
    }
}

/* 拖拽增强动画 */
.sortable-chosen {
    animation: dragStart var(--duration-200) var(--ease-out);
}

@keyframes dragStart {
    0% {
        transform: scale(1) rotate(0deg);
        box-shadow: var(--shadow-md);
    }
    100% {
        transform: scale(1.03) rotate(1deg);
        box-shadow: var(--shadow-2xl), var(--shadow-glow);
    }
}

.sortable-ghost {
    animation: ghostFloat var(--duration-300) var(--ease-out);
}

@keyframes ghostFloat {
    0% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
    100% {
        opacity: 0.3;
        transform: scale(0.98) rotate(3deg);
    }
}

/* 模态框增强动画 */
.modal-backdrop {
    animation: backdropFadeIn var(--duration-300) var(--ease-out);
}

@keyframes backdropFadeIn {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(12px) saturate(1.2);
    }
}

/* 成功状态动画 */
.animate-success {
    animation: successPulse var(--duration-500) var(--ease-out);
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

/* 错误状态动画 */
.animate-error {
    animation: errorShake var(--duration-500) var(--ease-out);
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-3px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(3px);
    }
}

/* 🌟 现代化加载系统 - Modern Loading System */

/* 🦴 骨架屏系统 - Skeleton Loading System */
.skeleton {
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
    border-radius: var(--radius-md);
    position: relative;
    overflow: hidden;
}

.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: skeletonShine 2s infinite;
}

@keyframes skeletonLoading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes skeletonShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 任务卡片骨架屏 */
.task-skeleton {
    padding: var(--space-4);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-4);
}

.task-skeleton-title {
    height: 20px;
    background: var(--neutral-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-3);
    width: 80%;
}

.task-skeleton-description {
    height: 14px;
    background: var(--neutral-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-2);
    width: 100%;
}

.task-skeleton-description:last-child {
    width: 60%;
}

.task-skeleton-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-3);
}

.task-skeleton-badge {
    height: 18px;
    width: 60px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
}

.task-skeleton-date {
    height: 12px;
    width: 40px;
    background: var(--neutral-200);
    border-radius: var(--radius-md);
}

/* 页面加载骨架屏 */
.page-skeleton {
    padding: var(--space-6);
}

.page-skeleton-header {
    height: 40px;
    background: var(--neutral-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    width: 300px;
}

.page-skeleton-nav {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.page-skeleton-nav-item {
    height: 36px;
    width: 120px;
    background: var(--neutral-200);
    border-radius: var(--radius-lg);
}

.page-skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.page-skeleton-column {
    background: var(--neutral-100);
    border-radius: var(--radius-xl);
    padding: var(--space-4);
    min-height: 400px;
}

.page-skeleton-column-header {
    height: 32px;
    background: var(--neutral-300);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
}

/* 🎭 暗色主题骨架屏 */
[data-theme="dark"] .skeleton {
    background: linear-gradient(90deg, var(--neutral-300) 25%, var(--neutral-200) 50%, var(--neutral-300) 75%);
}

[data-theme="dark"] .task-skeleton-title,
[data-theme="dark"] .task-skeleton-description,
[data-theme="dark"] .task-skeleton-badge,
[data-theme="dark"] .task-skeleton-date,
[data-theme="dark"] .page-skeleton-header,
[data-theme="dark"] .page-skeleton-nav-item {
    background: var(--neutral-300);
}

[data-theme="dark"] .page-skeleton-column {
    background: var(--neutral-200);
}

[data-theme="dark"] .page-skeleton-column-header {
    background: var(--neutral-400);
}

/* 现代化加载指示器 */
.loading-modern {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.loading-ring {
    width: 24px;
    height: 24px;
    border: 3px solid var(--neutral-200);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: loadingRing var(--duration-1000) linear infinite;
}

@keyframes loadingRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 脉冲加载动画 */
.loading-pulse {
    display: inline-flex;
    gap: var(--space-1);
}

.loading-pulse span {
    width: 8px;
    height: 8px;
    background: var(--primary-500);
    border-radius: 50%;
    animation: loadingPulse 1.4s infinite ease-in-out both;
}

.loading-pulse span:nth-child(1) { animation-delay: -0.32s; }
.loading-pulse span:nth-child(2) { animation-delay: -0.16s; }
.loading-pulse span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingPulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 波浪加载动画 */
.loading-wave {
    display: inline-flex;
    gap: 2px;
}

.loading-wave span {
    width: 4px;
    height: 20px;
    background: var(--primary-500);
    border-radius: 2px;
    animation: loadingWave 1.2s infinite ease-in-out;
}

.loading-wave span:nth-child(1) { animation-delay: -1.1s; }
.loading-wave span:nth-child(2) { animation-delay: -1.0s; }
.loading-wave span:nth-child(3) { animation-delay: -0.9s; }
.loading-wave span:nth-child(4) { animation-delay: -0.8s; }
.loading-wave span:nth-child(5) { animation-delay: -0.7s; }

@keyframes loadingWave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
        opacity: 0.5;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* 🎯 状态指示器系统 - Status Indicator System */

/* 成功指示器 */
.status-success {
    color: var(--success-600);
    animation: statusSuccess var(--duration-500) var(--ease-out);
}

@keyframes statusSuccess {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 错误指示器 */
.status-error {
    color: var(--danger-600);
    animation: statusError var(--duration-500) var(--ease-out);
}

@keyframes statusError {
    0% {
        transform: scale(0.8) rotate(-5deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(2deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 警告指示器 */
.status-warning {
    color: var(--warning-600);
    animation: statusWarning var(--duration-500) var(--ease-out);
}

@keyframes statusWarning {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    25% {
        transform: scale(1.05);
        opacity: 0.6;
    }
    50% {
        transform: scale(0.95);
        opacity: 0.8;
    }
    75% {
        transform: scale(1.02);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 🎨 进度指示器 - Progress Indicators */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width var(--duration-300) var(--ease-out);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 🎯 现代化响应式增强 - Modern Responsive Enhancements */

/* 容器系统 */
.container-fluid {
    width: 100%;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
    margin-left: auto;
    margin-right: auto;
}

/* 确保主要内容区域居中 */
main.container {
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
}

.container {
    width: 100% !important;
    padding-left: var(--space-4) !important;
    padding-right: var(--space-4) !important;
    margin-left: auto !important;
    margin-right: auto !important;
    max-width: 1200px !important; /* 设置默认最大宽度 */
    box-sizing: border-box !important;
}

@media (min-width: 640px) {
    .container {
        max-width: 600px !important;
        padding-left: var(--space-6) !important;
        padding-right: var(--space-6) !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px !important;
        padding-left: var(--space-8) !important;
        padding-right: var(--space-8) !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 960px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1140px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1200px !important; /* 限制最大宽度，避免过宽 */
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

/* 网格系统增强 */
.grid-responsive {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: 1fr;
    width: 100%;
    margin: 0;
    padding: 0;
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
}

/* 移动端优化 */
@media (max-width: 767px) {
    /* 隐藏桌面端元素 */
    .hidden-mobile {
        display: none !important;
    }

    /* 移动端专用样式 */
    .mobile-only {
        display: block !important;
    }

    /* 移动端任务卡片堆叠 */
    .grid-responsive {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    /* 移动端模态框全屏 */
    .modal-content {
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        margin: 0;
        border-radius: 0;
    }

    /* 移动端表单优化 */
    .form-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: var(--space-4);
    }

    /* 移动端按钮优化 */
    .btn-primary {
        padding: var(--space-4) var(--space-6);
        font-size: 1rem;
        min-height: 44px; /* 符合触摸标准 */
    }

    /* 移动端任务操作按钮 */
    .task-actions {
        opacity: 1;
        gap: var(--space-2);
    }

    /* 移动端紧凑统计面板优化 */
    .bg-white.rounded-lg.shadow-sm.border.p-4.mb-6 .flex.flex-wrap {
        flex-direction: column;
        gap: var(--space-4);
    }

    .bg-white.rounded-lg.shadow-sm.border.p-4.mb-6 .flex.items-center.space-x-6 {
        flex-wrap: wrap;
        gap: var(--space-3);
        justify-content: space-between;
    }

    .bg-white.rounded-lg.shadow-sm.border.p-4.mb-6 .w-16.h-16 {
        width: 3rem;
        height: 3rem;
    }
}

    .task-action-btn {
        padding: var(--space-3);
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1023px) {
    .tablet-only {
        display: block !important;
    }

    .hidden-tablet {
        display: none !important;
    }

    /* 平板端网格布局 */
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }

    /* 平板端任务卡片 */
    .task-card {
        padding: var(--space-4);
    }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
    .desktop-only {
        display: block !important;
    }

    .hidden-desktop {
        display: none !important;
    }

    /* 桌面端悬浮效果增强 */
    .task-card:hover {
        transform: translateY(-8px) scale(1.02);
    }

    /* 桌面端任务操作按钮 */
    .task-actions {
        opacity: 0;
        transition: opacity var(--duration-200) var(--ease-out);
    }

    .task-card:hover .task-actions {
        opacity: 1;
    }
}

/* 超大屏幕优化 */
@media (min-width: 1536px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .task-card {
        padding: var(--space-6);
    }

    .task-title {
        font-size: 1.25rem;
    }

    .task-description {
        font-size: 1rem;
        -webkit-line-clamp: 4;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 触摸设备上始终显示操作按钮 */
    .task-actions {
        opacity: 1 !important;
    }

    /* 增大触摸目标 */
    .task-action-btn {
        min-width: 48px;
        min-height: 48px;
        padding: var(--space-3);
    }

    /* 移除悬浮效果 */
    .task-card:hover {
        transform: none;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .task-card {
        border-width: 2px;
        border-color: var(--neutral-900);
    }

    .btn-primary {
        border: 2px solid var(--neutral-900);
    }

    .priority-badge {
        border-width: 2px;
        font-weight: 700;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .task-card:hover {
        transform: none;
    }

    .btn-primary:hover {
        transform: none;
    }
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 全屏加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-overlay .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 骨架屏加载 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* 操作反馈动画 */
.feedback-success {
    animation: successPulse 0.6s ease-out;
}

.feedback-error {
    animation: errorShake 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); background-color: #10b981; }
    100% { transform: scale(1); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 按钮加载状态 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 进度指示器 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 操作确认动画 */
.confirm-action {
    animation: confirmBounce 0.4s ease-out;
}

@keyframes confirmBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 成功/错误提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    z-index: 9999;
    animation: toastSlideIn 0.3s ease-out;
}

.toast-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.toast-error {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .task-card {
        margin-bottom: 0.75rem;
    }
    
    .column-header h2 {
        font-size: 1rem;
    }
    
    .btn-primary {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* 暗色主题样式 */
[data-theme="dark"] .task-card {
    background: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--gray-900);
}

[data-theme="dark"] .task-card:hover {
    border-color: var(--primary-color);
}

[data-theme="dark"] body {
    background: var(--gray-50);
    color: var(--gray-900);
}

/* 微交互动画 */
.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 连接状态指示器样式已移除 */

/* 数据同步状态 */
.sync-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    transform: translateY(100px);
    transition: transform 0.3s ease;
}

.sync-indicator.show {
    transform: translateY(0);
}

.sync-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 备份恢复界面 */
.backup-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    margin: 20px auto;
}

.backup-button {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    border: 2px dashed var(--gray-300);
    border-radius: 8px;
    background: var(--gray-50);
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s ease;
}

.backup-button:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.backup-button.active {
    border-color: var(--success-color);
    background: var(--success-color);
    color: white;
}

/* 离线模式样式 */
.offline-mode {
    filter: grayscale(20%);
    opacity: 0.9;
}

.offline-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 9999;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.offline-banner.show {
    transform: translateY(0);
}

/* 自动保存指示器 */
.autosave-indicator {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.autosave-indicator.show {
    opacity: 1;
}

/* 高级视觉效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-border {
    position: relative;
    background: white;
    border-radius: 8px;
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

/* 悬浮效果增强 */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 渐变文字 */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 发光效果 */
.glow-effect {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

/* 粒子背景效果 */
.particles-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* 任务卡片增强效果 */
.task-card-enhanced {
    position: relative;
    overflow: hidden;
}

.task-card-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.task-card-enhanced:hover::after {
    left: 100%;
}

/* 时间进度提示系统 */
.time-progress-indicator {
    margin-top: 0.5rem;
    padding: 0.25rem 0;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: var(--neutral-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--neutral-600);
    font-weight: 500;
}

/* 进度状态颜色 */
.progress-fill.overdue {
    background: linear-gradient(90deg, var(--red-500), var(--red-400));
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--yellow-500), var(--yellow-400));
}

.progress-fill.completed {
    background: linear-gradient(90deg, var(--green-500), var(--green-400));
}

/* 状态指示器动画 */
.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transform: translateY(-50%);
}

.status-todo::before {
    background: #3b82f6;
    animation: pulse-blue 2s infinite;
}

.status-progress::before {
    background: #f59e0b;
    animation: pulse-yellow 2s infinite;
}

.status-done::before {
    background: #10b981;
    animation: pulse-green 2s infinite;
}

@keyframes pulse-blue {
    0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
}

@keyframes pulse-yellow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(245, 158, 11, 0); }
}

@keyframes pulse-green {
    0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
}

/* 加载屏幕美化 */
#loadingScreen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
}

/* 加载状态样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮加载状态 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 骨架屏样式 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 20px;
    width: 60%;
}

.skeleton-text {
    height: 16px;
    width: 100%;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #f3f4f6;
    border-radius: 2px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 操作反馈样式 */
.feedback-success {
    animation: successPulse 0.6s ease;
}

.feedback-error {
    animation: errorShake 0.6s ease;
}

.confirm-action {
    animation: confirmBounce 0.4s ease;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); background-color: #dcfce7; }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); background-color: #fef2f2; }
    75% { transform: translateX(5px); background-color: #fef2f2; }
}

@keyframes confirmBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(0.95); }
}

/* 在线状态指示器样式已移除 */

/* 在线用户指示器 */
.online-users {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    z-index: 1000;
}

/* 分析模态框样式 */
.analytics-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
}

/* 暗色主题适配 */
[data-theme="dark"] .loading-overlay {
    background: rgba(31, 41, 55, 0.9);
}

[data-theme="dark"] .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

[data-theme="dark"] .progress-bar {
    background: #374151;
}

/* 富文本编辑器增强样式 */
/* 富文本编辑器整体容器 - 简洁设计 */
.rich-text-wrapper {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
    background: white;
}

.ql-container {
    font-size: 14px;
    border: none;
    border-radius: 0;
}

.ql-toolbar, #toolbar-container {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 12px;
    background: #f9fafb;
    margin: 0;
    position: relative;
    z-index: 1;
}

#toolbar-container .ql-formats {
    margin-right: 8px;
    display: inline-block;
}

#toolbar-container .ql-formats:not(:last-child) {
    border-right: 1px solid #ddd;
    padding-right: 8px;
}

#toolbar-container button, #toolbar-container select {
    margin: 1px 2px;
    padding: 5px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    min-height: 30px;
}

#toolbar-container button:hover, #toolbar-container select:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

/* 特殊处理Quill选择器 - 简洁美观 */
#toolbar-container .ql-size,
#toolbar-container .ql-color,
#toolbar-container .ql-background {
    min-width: 60px;
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    padding: 5px 8px !important;
    margin: 1px 2px !important;
    font-size: 13px !important;
    height: 30px !important;
}

#toolbar-container .ql-size:hover,
#toolbar-container .ql-color:hover,
#toolbar-container .ql-background:hover {
    background-color: #f3f4f6 !important;
    border-color: #9ca3af !important;
}

/* 覆盖Quill默认样式 - 简洁设计 */
.ql-toolbar .ql-picker {
    color: #374151 !important;
}

.ql-toolbar .ql-picker-options {
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.ql-toolbar .ql-picker-item:hover {
    background-color: #f3f4f6 !important;
}

/* 确保所有工具栏元素统一样式 */
#toolbar-container * {
    box-sizing: border-box;
}

#toolbar-container .ql-formats {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

/* 确保只显示我们的自定义工具栏 */
.ql-toolbar.ql-snow:not(#toolbar-container) {
    display: none !important;
}

/* 确保自定义工具栏可见并正确样式化 */
#toolbar-container {
    display: block !important;
    visibility: visible !important;
}

/* 确保Quill容器不会创建额外的工具栏 */
.ql-container .ql-toolbar {
    display: none !important;
}

/* 强制隐藏任何自动生成的工具栏 */
.ql-snow .ql-toolbar:not(#toolbar-container) {
    display: none !important;
    height: 0 !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    visibility: hidden !important;
    position: absolute !important;
    top: -9999px !important;
    left: -9999px !important;
}

/* 隐藏所有可能的Quill工具栏变体 */
.ql-toolbar,
.ql-snow .ql-toolbar,
div[class*="ql-toolbar"],
.rich-text-wrapper .ql-toolbar:not(#toolbar-container) {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* 确保我们的工具栏始终可见 */
#toolbar-container,
#toolbar-container.ql-toolbar {
    display: block !important;
    height: auto !important;
    overflow: visible !important;
    visibility: visible !important;
    position: relative !important;
    top: auto !important;
    left: auto !important;
}

#toolbar-container button.ql-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.ql-editor {
    min-height: 180px;
    font-size: 14px;
    line-height: 1.6;
    padding: 12px 15px;
    border-top: none;
}

.ql-editor.ql-blank::before {
    font-style: normal;
    color: #999;
    font-size: 13px;
    line-height: 1.4;
}

/* 工具栏按钮样式优化 */
.ql-toolbar .ql-formats {
    margin-right: 8px;
}

.ql-toolbar .ql-formats:not(:last-child) {
    border-right: 1px solid #ddd;
    padding-right: 8px;
}

.ql-toolbar button {
    padding: 4px 6px;
    margin: 1px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.ql-toolbar button:hover {
    background-color: #e6e6e6;
}

.ql-toolbar button.ql-active {
    background-color: #06c;
    color: white;
}

/* 拖拽上传效果 */
.ql-editor.drag-over {
    border: 2px dashed var(--primary-color) !important;
    background-color: rgba(59, 130, 246, 0.05) !important;
    position: relative;
}

/* 附件样式美化 - 简化版 */
.ql-editor .attachment-item {
    display: inline-block !important;
    margin: 2px 6px 2px 0 !important;
    padding: 6px 10px !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border: 1px solid #cbd5e1 !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    color: #374151 !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
}

.ql-editor .attachment-item:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.ql-editor .attachment-item .attachment-link {
    text-decoration: none !important;
    color: inherit !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.ql-editor .attachment-item .file-icon {
    font-size: 14px !important;
    flex-shrink: 0 !important;
}

.ql-editor .attachment-item .file-name {
    font-weight: 500 !important;
    color: #1f2937 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 200px !important;
    line-height: 1.3 !important;
}

.ql-editor.drag-over::before {
    content: '拖拽文件到此处上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color);
    font-weight: 600;
    font-size: 16px;
    pointer-events: none;
    z-index: 10;
}

/* 自定义附件按钮样式 */
.ql-toolbar .ql-attachment {
    background: none;
    border: none;
    cursor: pointer;
    display: inline-block;
    height: 28px;
    padding: 4px 6px;
    width: 32px;
    text-align: center;
    border-radius: 3px;
    transition: all 0.2s ease;
    position: relative;
}

.ql-toolbar .ql-attachment svg {
    width: 18px;
    height: 18px;
    color: #444;
}

.ql-toolbar .ql-attachment:hover {
    background-color: #e6e6e6;
}

.ql-toolbar .ql-attachment:hover svg {
    color: #06c;
}

.ql-toolbar .ql-attachment:active {
    background-color: #d4d4d4;
}

/* 附件按钮提示 */
.ql-toolbar .ql-attachment::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.ql-toolbar .ql-attachment:hover::after {
    opacity: 1;
}

/* 附件链接样式 */
.ql-editor .attachment-link {
    display: inline-flex;
    align-items: center;
    padding: 6px 10px;
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    text-decoration: none;
    color: var(--gray-700);
    font-size: 13px;
    margin: 2px 4px;
    transition: all 0.2s ease;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ql-editor .attachment-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 视频链接特殊样式 */
.ql-editor .video-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.ql-editor .video-link:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 图片样式优化 */
.ql-editor img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
    margin: 4px 0;
}

/* 任务详情模态框中的图片样式 */
#taskDetailModal img,
#taskDetailModal .ql-editor img,
.task-detail-content img {
    max-width: 100%;
    max-height: 400px;
    height: auto;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    margin: 8px 0;
    object-fit: contain;
    display: block;
}

/* 任务详情描述区域样式 */
#detailDescription {
    max-width: 100%;
    overflow-x: auto;
    word-wrap: break-word;
}

#detailDescription img {
    max-width: 100%;
    max-height: 300px;
    height: auto;
    border-radius: 6px;
    margin: 6px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 暗色主题适配 */
[data-theme="dark"] .ql-toolbar .ql-attachment:hover {
    background-color: var(--gray-200);
}

[data-theme="dark"] .ql-editor .attachment-link {
    background-color: var(--gray-200);
    border-color: var(--gray-300);
    color: var(--gray-600);
}

[data-theme="dark"] .ql-editor .attachment-link:hover {
    background-color: var(--gray-300);
    border-color: var(--gray-400);
}

/* 附件选择菜单样式 */
.attachment-menu {
    animation: fadeInScale 0.2s ease-out;
    box-shadow: var(--shadow-lg);
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.attachment-option {
    transition: all 0.15s ease;
    cursor: pointer;
}

.attachment-option:hover {
    background-color: var(--gray-100);
    transform: translateX(2px);
}

.attachment-option:active {
    background-color: var(--gray-200);
    transform: translateX(1px);
}

/* 富文本编辑器增强提示 */
.ql-editor:empty::before {
    content: attr(data-placeholder);
    color: var(--gray-400);
    font-style: italic;
    pointer-events: none;
    position: absolute;
}

/* 附件链接悬停效果增强 */
.ql-editor .attachment-link {
    position: relative;
    overflow: hidden;
}

.ql-editor .attachment-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.ql-editor .attachment-link:hover::before {
    left: 100%;
}

/* 暗色主题适配 */
[data-theme="dark"] .attachment-menu {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
}

[data-theme="dark"] .attachment-option:hover {
    background-color: var(--gray-200);
}

[data-theme="dark"] .attachment-option:active {
    background-color: var(--gray-300);
}

/* 🔍 查看更多任务功能样式 - Show More Tasks Styles */
.show-more-tasks-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.show-more-tasks-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.show-more-tasks-btn:active {
    transform: translateY(0);
}

.show-more-tasks-btn svg {
    transition: transform 0.3s ease-in-out;
}

.show-more-tasks-btn:hover svg {
    transform: translateY(1px);
}

/* 任务卡片隐藏状态动画 */
.task-card-hidden {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

.task-card-hidden:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

/* 历史任务视觉区分 */
.task-card-hidden:not(.hidden) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.task-card-hidden:not(.hidden):hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
    border-color: rgba(203, 213, 225, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 深色主题下的历史任务样式 */
[data-theme="dark"] .task-card-hidden:not(.hidden) {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
    border: 1px solid rgba(51, 65, 85, 0.8);
}

[data-theme="dark"] .task-card-hidden:not(.hidden):hover {
    background: linear-gradient(135deg, rgba(30, 41, 59, 1) 0%, rgba(15, 23, 42, 1) 100%);
    border-color: rgba(71, 85, 105, 1);
}

/* 富文本工具栏图标美化 */
/* 基础按钮样式重置 */
#toolbar-container button {
    position: relative;
    overflow: visible;
}

#toolbar-container button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1;
}

/* 隐藏默认的Quill图标 */
#toolbar-container button .ql-stroke,
#toolbar-container button .ql-fill {
    display: none !important;
}

/* 粗体图标 */
#toolbar-container .ql-bold::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z'/%3E%3Cpath d='M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z'/%3E%3C/svg%3E");
}

/* 斜体图标 */
#toolbar-container .ql-italic::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='19' y1='4' x2='10' y2='4'/%3E%3Cline x1='14' y1='20' x2='5' y2='20'/%3E%3Cline x1='15' y1='4' x2='9' y2='20'/%3E%3C/svg%3E");
}

/* 下划线图标 */
#toolbar-container .ql-underline::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3'/%3E%3Cline x1='4' y1='21' x2='20' y2='21'/%3E%3C/svg%3E");
}

/* 删除线图标 */
#toolbar-container .ql-strike::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 16c0 2.2 1.8 4 4 4s4-1.8 4-4c0-1.5-.7-2.8-1.8-3.5'/%3E%3Cpath d='M6 10c0-2.2 1.8-4 4-4s4 1.8 4 4c0 .6-.1 1.2-.4 1.7'/%3E%3Cline x1='3' y1='12' x2='21' y2='12'/%3E%3C/svg%3E");
}

/* 引用图标 */
#toolbar-container .ql-blockquote::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z'/%3E%3Cpath d='M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z'/%3E%3C/svg%3E");
}

/* 代码块图标 */
#toolbar-container .ql-code-block::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16,18 22,12 16,6'/%3E%3Cpolyline points='8,6 2,12 8,18'/%3E%3C/svg%3E");
}

/* 标题图标 */
#toolbar-container .ql-header[value="1"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12h8m-8-6v12m8-6v6m5-10v6h3'/%3E%3C/svg%3E");
}

#toolbar-container .ql-header[value="2"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12h8m-8-6v12m8-6v6m5-10v6h3'/%3E%3C/svg%3E");
}

/* 有序列表图标 */
#toolbar-container .ql-list[value="ordered"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='10' y1='6' x2='21' y2='6'/%3E%3Cline x1='10' y1='12' x2='21' y2='12'/%3E%3Cline x1='10' y1='18' x2='21' y2='18'/%3E%3Cpath d='M4 6h1v4'/%3E%3Cpath d='M4 10h2'/%3E%3Cpath d='M6 18H4c0-1 0-2 1-3s1-1 1-1'/%3E%3Cpath d='M4 14c1 0 1-1 2-2s1-1 0-1-1 1-2 2'/%3E%3C/svg%3E");
}

/* 无序列表图标 */
#toolbar-container .ql-list[value="bullet"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='8' y1='6' x2='21' y2='6'/%3E%3Cline x1='8' y1='12' x2='21' y2='12'/%3E%3Cline x1='8' y1='18' x2='21' y2='18'/%3E%3Cline x1='3' y1='6' x2='3.01' y2='6'/%3E%3Cline x1='3' y1='12' x2='3.01' y2='12'/%3E%3Cline x1='3' y1='18' x2='3.01' y2='18'/%3E%3C/svg%3E");
}

/* 链接图标 */
#toolbar-container .ql-link::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71'/%3E%3Cpath d='M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71'/%3E%3C/svg%3E");
}

/* 图片图标 */
#toolbar-container .ql-image::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21,15 16,10 5,21'/%3E%3C/svg%3E");
}

/* 附件图标 */
#toolbar-container .ql-attachment::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48'/%3E%3C/svg%3E");
}

/* 清除格式图标 */
#toolbar-container .ql-clean::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3,6 5,6 21,6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3Cline x1='10' y1='11' x2='10' y2='17'/%3E%3Cline x1='14' y1='11' x2='14' y2='17'/%3E%3C/svg%3E");
}

/* 激活状态的图标颜色 */
#toolbar-container button.ql-active::before {
    filter: brightness(0) saturate(100%) invert(100%);
}

/* 悬停状态的图标颜色 */
#toolbar-container button:hover::before {
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

/* 选择器样式优化 */
#toolbar-container .ql-size,
#toolbar-container .ql-color,
#toolbar-container .ql-background {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

#toolbar-container .ql-size::after,
#toolbar-container .ql-color::after,
#toolbar-container .ql-background::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #6b7280;
    pointer-events: none;
}

/* 颜色选择器特殊样式 */
#toolbar-container .ql-color .ql-picker-label,
#toolbar-container .ql-background .ql-picker-label {
    position: relative;
    padding-left: 20px;
}

#toolbar-container .ql-color .ql-picker-label::before {
    content: '';
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 1px solid #d1d5db;
    border-radius: 2px;
    background: linear-gradient(45deg, #ff0000 0%, #ff7f00 14%, #ffff00 28%, #00ff00 42%, #0000ff 57%, #4b0082 71%, #9400d3 85%, #ff0000 100%);
}

#toolbar-container .ql-background .ql-picker-label::before {
    content: '';
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 1px solid #d1d5db;
    border-radius: 2px;
    background: linear-gradient(45deg, #ffffff 0%, #f3f4f6 50%, #e5e7eb 100%);
}

/* 🎯 标题栏信息面板样式 - Header Info Panel */
.header-info-panel {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* 优先级徽章样式 */
.header-priority-badge {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-priority-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 进度圆环动画 */
.header-progress-circle {
    transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 时间状态样式 */
.header-time-status {
    font-variant-numeric: tabular-nums;
    letter-spacing: 0.025em;
}

/* 移动端优化 */
@media (max-width: 640px) {
    .header-info-panel {
        padding: 0.25rem 0.5rem;
    }

    .header-priority-mobile {
        font-size: 1.125rem;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        transition: transform 0.2s ease;
    }

    .header-priority-mobile:hover {
        transform: scale(1.1);
    }

    .header-progress-mobile {
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        min-width: 2rem;
        text-align: center;
    }

    /* 移动端标题栏调整 */
    .modal-header-mobile {
        padding: 0.75rem 1rem;
    }

    .modal-header-mobile h3 {
        font-size: 1rem;
        line-height: 1.25;
    }

    .modal-header-mobile .task-id {
        font-size: 0.75rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .header-priority-mobile {
        font-size: 1rem;
    }

    .header-progress-mobile {
        font-size: 0.75rem;
    }

    .modal-header-mobile {
        padding: 0.5rem 0.75rem;
    }
}

/* 响应式隐藏/显示 */
@media (min-width: 640px) {
    .header-mobile-only {
        display: none !important;
    }
}

@media (max-width: 639px) {
    .header-desktop-only {
        display: none !important;
    }
}
