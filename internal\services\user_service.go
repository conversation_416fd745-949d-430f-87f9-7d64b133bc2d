package services

import (
	"crypto/sha256"
	"fmt"
	"projectm2/internal/models"

	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建新的用户服务实例
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// GetAllUsers 获取所有用户（包括停用的，但不包括软删除的）
func (s *UserService) GetAllUsers() ([]models.UserListResponse, error) {
	var users []models.User
	// 移除 is_active 过滤条件，显示所有未删除的用户
	err := s.db.Order("is_active DESC, full_name").Find(&users).Error
	if err != nil {
		return nil, err
	}

	// 转换为UserListResponse格式
	var userList []models.UserListResponse
	for _, user := range users {
		userList = append(userList, user.ToListResponse())
	}

	return userList, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id string) (*models.User, error) {
	var user models.User
	err := s.db.First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	err := s.db.First(&user, "username = ?", username).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// CreateUser 创建新用户
func (s *UserService) CreateUser(req models.UserCreateRequest) (*models.User, error) {
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password, // 注意：这里应该传入已加密的密码
		FullName: req.FullName,
		Role:     req.Role,
		Avatar:   req.Avatar,
		IsActive: true,
	}

	// 设置默认角色
	if user.Role == "" {
		user.Role = models.RoleDeveloper
	}

	err := s.db.Create(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// CheckUserExists 检查用户名或邮箱是否已存在（只检查未删除的用户）
func (s *UserService) CheckUserExists(username, email string) (bool, error) {
	var count int64
	// 明确排除软删除的记录
	err := s.db.Model(&models.User{}).Where("username = ? OR email = ?", username, email).Where("deleted_at IS NULL").Count(&count).Error
	return count > 0, err
}

// CheckActiveUserExists 检查活跃用户中是否存在相同用户名或邮箱
func (s *UserService) CheckActiveUserExists(username, email string) (bool, error) {
	var count int64
	err := s.db.Model(&models.User{}).Where("(username = ? OR email = ?) AND is_active = ?", username, email, true).Where("deleted_at IS NULL").Count(&count).Error
	return count > 0, err
}

// FindInactiveUserByUsernameOrEmail 查找停用的用户
func (s *UserService) FindInactiveUserByUsernameOrEmail(username, email string) (*models.User, error) {
	var user models.User
	err := s.db.Where("(username = ? OR email = ?) AND is_active = ?", username, email, false).Where("deleted_at IS NULL").First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(id string, req models.UserUpdateRequest) (*models.User, error) {
	user, err := s.GetUserByID(id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.FullName != "" {
		user.FullName = req.FullName
	}
	if req.Role != "" {
		user.Role = req.Role
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	// 只有明确提供了 IsActive 字段时才更新
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	// 如果提供了新密码，则更新密码
	if req.Password != "" {
		user.Password = s.hashPassword(req.Password)
	}

	err = s.db.Save(user).Error
	if err != nil {
		return nil, err
	}

	return user, nil
}

// hashPassword 加密密码（内部方法）
func (s *UserService) hashPassword(password string) string {
	// 使用与AuthService相同的加密方法
	hash := sha256.Sum256([]byte(password))
	return fmt.Sprintf("%x", hash)
}

// DeleteUser 删除用户（软删除）
func (s *UserService) DeleteUser(id string) error {
	return s.db.Delete(&models.User{}, "id = ?", id).Error
}

// DeactivateUser 停用用户
func (s *UserService) DeactivateUser(id string) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", false).Error
}

// ActivateUser 激活用户
func (s *UserService) ActivateUser(id string) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", true).Error
}

// GetActiveUsers 获取活跃用户列表（用于任务分配）
func (s *UserService) GetActiveUsers() ([]models.UserListResponse, error) {
	var users []models.User
	err := s.db.Where("is_active = ?", true).Order("full_name").Find(&users).Error
	if err != nil {
		return nil, err
	}

	var userList []models.UserListResponse
	for _, user := range users {
		userList = append(userList, user.ToListResponse())
	}

	return userList, nil
}

// GetUsersByRole 根据角色获取用户
func (s *UserService) GetUsersByRole(role models.UserRole) ([]models.User, error) {
	var users []models.User
	err := s.db.Where("role = ? AND is_active = ?", role, true).Order("full_name").Find(&users).Error
	return users, err
}

// GetUserTaskStats 获取用户任务统计
func (s *UserService) GetUserTaskStats(userID string) (map[string]int, error) {
	stats := make(map[string]int)

	// 统计分配给用户的任务
	var assignedCounts []struct {
		Status models.TaskStatus
		Count  int
	}

	err := s.db.Model(&models.Task{}).
		Select("status, count(*) as count").
		Where("assigned_to = ?", userID).
		Group("status").
		Scan(&assignedCounts).Error

	if err != nil {
		return nil, err
	}

	// 初始化统计数据
	stats["total_assigned"] = 0
	stats["todo"] = 0
	stats["in_progress"] = 0
	stats["done"] = 0

	for _, count := range assignedCounts {
		stats["total_assigned"] += count.Count
		switch count.Status {
		case models.StatusTodo:
			stats["todo"] = count.Count
		case models.StatusInProgress:
			stats["in_progress"] = count.Count
		case models.StatusDone:
			stats["done"] = count.Count
		}
	}

	// 统计用户创建的任务
	var createdCount int64
	s.db.Model(&models.Task{}).Where("created_by = ?", userID).Count(&createdCount)
	stats["total_created"] = int(createdCount)

	return stats, nil
}

// GetPendingUsers 获取待审核用户列表
func (s *UserService) GetPendingUsers() ([]models.UserListResponse, error) {
	var users []models.User
	err := s.db.Where("status = ?", models.StatusPending).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return nil, err
	}

	// 转换为UserListResponse格式
	var userList []models.UserListResponse
	for _, user := range users {
		userList = append(userList, user.ToListResponse())
	}

	return userList, nil
}

// GetUsersByStatus 根据状态获取用户列表
func (s *UserService) GetUsersByStatus(status models.UserStatus) ([]models.UserListResponse, error) {
	var users []models.User
	err := s.db.Where("status = ?", status).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return nil, err
	}

	// 转换为UserListResponse格式
	var userList []models.UserListResponse
	for _, user := range users {
		userList = append(userList, user.ToListResponse())
	}

	return userList, nil
}

// ApproveUser 批准用户
func (s *UserService) ApproveUser(userID string) error {
	// 检查用户是否存在
	var user models.User
	err := s.db.First(&user, "id = ?", userID).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 检查用户是否为待审核状态
	if user.Status != models.StatusPending {
		return fmt.Errorf("用户状态不是待审核，无法批准")
	}

	// 更新用户状态为激活
	user.Status = models.StatusActive
	user.IsActive = true

	return s.db.Save(&user).Error
}

// RejectUser 拒绝用户
func (s *UserService) RejectUser(userID string) error {
	// 检查用户是否存在
	var user models.User
	err := s.db.First(&user, "id = ?", userID).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 检查用户是否为待审核状态
	if user.Status != models.StatusPending {
		return fmt.Errorf("用户状态不是待审核，无法拒绝")
	}

	// 更新用户状态为已拒绝
	user.Status = models.StatusRejected
	user.IsActive = false

	return s.db.Save(&user).Error
}

// DisableUser 禁用用户
func (s *UserService) DisableUser(userID string) error {
	// 检查用户是否存在
	var user models.User
	err := s.db.First(&user, "id = ?", userID).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 更新用户状态为已禁用
	user.Status = models.StatusDisabled
	user.IsActive = false

	return s.db.Save(&user).Error
}

// EnableUser 启用用户（从禁用状态恢复）
func (s *UserService) EnableUser(userID string) error {
	// 检查用户是否存在
	var user models.User
	err := s.db.First(&user, "id = ?", userID).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 更新用户状态为激活
	user.Status = models.StatusActive
	user.IsActive = true

	return s.db.Save(&user).Error
}

// GetUserStatusCounts 获取各状态用户数量统计
func (s *UserService) GetUserStatusCounts() (map[string]int64, error) {
	counts := make(map[string]int64)

	// 统计各状态用户数量
	var statusCounts []struct {
		Status models.UserStatus `json:"status"`
		Count  int64             `json:"count"`
	}

	err := s.db.Model(&models.User{}).
		Select("status, count(*) as count").
		Group("status").
		Scan(&statusCounts).Error

	if err != nil {
		return nil, err
	}

	// 初始化所有状态的计数
	counts["pending"] = 0
	counts["active"] = 0
	counts["rejected"] = 0
	counts["disabled"] = 0

	// 填充实际计数
	for _, count := range statusCounts {
		switch count.Status {
		case models.StatusPending:
			counts["pending"] = count.Count
		case models.StatusActive:
			counts["active"] = count.Count
		case models.StatusRejected:
			counts["rejected"] = count.Count
		case models.StatusDisabled:
			counts["disabled"] = count.Count
		}
	}

	// 计算总数
	var total int64
	s.db.Model(&models.User{}).Count(&total)
	counts["total"] = total

	return counts, nil
}
