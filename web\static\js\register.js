// 注册页面交互增强
// Register Page Interactive Enhancements

document.addEventListener('DOMContentLoaded', function() {
    initializeRegisterPage();
});

// 初始化注册页面
function initializeRegisterPage() {
    setupFormValidation();
    setupKeyboardShortcuts();
    setupAnimations();
    setupThemeToggle();
}

// 设置表单验证
function setupFormValidation() {
    const inputs = document.querySelectorAll('.form-input');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// 验证单个字段
function validateField(input) {
    const value = input.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    switch(input.id) {
        case 'username':
            if (value.length < 3 || value.length > 50) {
                isValid = false;
                errorMessage = '用户名长度应在3-50个字符之间';
            } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(value)) {
                isValid = false;
                errorMessage = '用户名只能包含字母、数字、下划线和中文';
            }
            break;
            
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = '请输入有效的邮箱地址';
            }
            break;
            
        case 'fullName':
            if (value.length < 1 || value.length > 100) {
                isValid = false;
                errorMessage = '姓名长度应在1-100个字符之间';
            }
            break;
            
        case 'password':
            if (value.length < 6 || value.length > 100) {
                isValid = false;
                errorMessage = '密码长度应在6-100个字符之间';
            }
            break;
            
        case 'confirmPassword':
            const password = document.getElementById('password').value;
            if (value !== password) {
                isValid = false;
                errorMessage = '两次输入的密码不一致';
            }
            break;
    }
    
    if (isValid) {
        setFieldValid(input);
    } else {
        setFieldError(input, errorMessage);
    }
    
    return isValid;
}

// 设置字段为有效状态
function setFieldValid(input) {
    input.classList.remove('border-red-500');
    input.classList.add('border-green-500');
    removeFieldErrorMessage(input);
}

// 设置字段错误状态
function setFieldError(input, message) {
    input.classList.remove('border-green-500');
    input.classList.add('border-red-500');
    showFieldErrorMessage(input, message);
}

// 清除字段错误状态
function clearFieldError(input) {
    input.classList.remove('border-red-500', 'border-green-500');
    removeFieldErrorMessage(input);
}

// 显示字段错误消息
function showFieldErrorMessage(input, message) {
    removeFieldErrorMessage(input);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error text-red-500 text-xs mt-1';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
}

// 移除字段错误消息
function removeFieldErrorMessage(input) {
    const existingError = input.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// 验证整个表单
function validateForm() {
    const inputs = document.querySelectorAll('.form-input');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + Enter 快速注册
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('registerForm').dispatchEvent(new Event('submit'));
        }
        
        // ESC 清空表单
        if (e.key === 'Escape') {
            clearForm();
        }
    });
}

// 清空表单
function clearForm() {
    const form = document.getElementById('registerForm');
    form.reset();
    
    // 清除所有验证状态
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        clearFieldError(input);
    });
    
    hideMessage();
}

// 设置动画效果
function setupAnimations() {
    // 输入框焦点动画
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.classList.add('input-focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentNode.classList.remove('input-focused');
        });
    });
}

// 设置主题切换
function setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const currentTheme = localStorage.getItem('theme') || 'light';
    
    // 应用保存的主题
    setTheme(currentTheme);
    
    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        setTheme(newTheme);
        updateThemeIcon(newTheme);
    });
}

// 设置主题
function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
}

// 更新主题图标
function updateThemeIcon(theme) {
    const button = document.getElementById('themeToggle');
    const icon = button.querySelector('svg path');
    
    if (theme === 'dark') {
        // 太阳图标
        icon.setAttribute('d', 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z');
    } else {
        // 月亮图标
        icon.setAttribute('d', 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z');
    }
}

// 显示消息
function showMessage(message, type) {
    const messageContainer = document.getElementById('message-container');
    const messageElement = document.getElementById('message');
    
    messageElement.textContent = message;
    messageElement.className = `p-4 rounded-xl text-sm font-medium ${type === 'error' ? 'error-message' : 'success-message'}`;
    
    messageContainer.classList.remove('hidden');
    
    // 自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            hideMessage();
        }, 5000);
    }
}

// 隐藏消息
function hideMessage() {
    const messageContainer = document.getElementById('message-container');
    messageContainer.classList.add('hidden');
}

// 处理注册
async function handleRegister(formData) {
    if (!validateForm()) {
        showMessage('请检查输入信息', 'error');
        return;
    }
    
    try {
        // 显示加载状态
        setRegisterButtonState(true, '注册中...', true);
        
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('注册成功！请等待管理员审核，审核通过后即可登录。', 'success');
            
            // 清空表单
            setTimeout(() => {
                clearForm();
            }, 2000);
            
            // 延迟跳转到登录页面
            setTimeout(() => {
                window.location.href = '/login';
            }, 5000);
        } else {
            showMessage(data.message || '注册失败', 'error');
            setRegisterButtonState(false, '注册', false);
        }
    } catch (error) {
        console.error('Register error:', error);
        showMessage('网络错误，请稍后重试', 'error');
        setRegisterButtonState(false, '注册', false);
    }
}

// 设置注册按钮状态
function setRegisterButtonState(disabled, text, showSpinner) {
    const registerBtn = document.getElementById('registerBtn');
    const registerText = document.getElementById('registerText');
    const registerSpinner = document.getElementById('registerSpinner');
    
    registerBtn.disabled = disabled;
    registerText.textContent = text;
    
    if (showSpinner) {
        registerSpinner.classList.remove('hidden');
    } else {
        registerSpinner.classList.add('hidden');
    }
}

// 注册表单提交处理
document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        username: document.getElementById('username').value.trim(),
        email: document.getElementById('email').value.trim(),
        full_name: document.getElementById('fullName').value.trim(),
        password: document.getElementById('password').value,
    };
    
    await handleRegister(formData);
});

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    updateThemeIcon(currentTheme);
    
    // 注册卡片入场动画
    const registerCard = document.querySelector('.register-card');
    registerCard.style.opacity = '0';
    registerCard.style.transform = 'translateY(30px) scale(0.95)';
    
    setTimeout(() => {
        registerCard.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        registerCard.style.opacity = '1';
        registerCard.style.transform = 'translateY(0) scale(1)';
    }, 150);
});

// 导出函数供全局使用
window.RegisterPage = {
    showMessage,
    hideMessage,
    handleRegister,
    validateForm,
    clearForm,
    setTheme
};
