package handlers

import (
	"net/http"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AttachmentHandler 附件处理器
type AttachmentHandler struct {
	attachmentService *services.AttachmentService
}

// NewAttachmentHandler 创建新的附件处理器实例
func NewAttachmentHandler(db *gorm.DB) *AttachmentHandler {
	return &AttachmentHandler{
		attachmentService: services.NewAttachmentService(db),
	}
}

// UploadFile 上传文件API
func (h *AttachmentHandler) UploadFile(c *gin.Context) {
	taskID := c.PostForm("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "获取文件失败"})
		return
	}

	// 验证文件类型
	if err := h.attachmentService.ValidateFileType(file.Filename); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证文件大小
	if err := h.attachmentService.ValidateFileSize(file.Size); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取上传者信息（暂时使用固定值，后续可以从认证信息中获取）
	uploadedBy := c.PostForm("uploaded_by")
	if uploadedBy == "" {
		uploadedBy = "system" // 默认值
	}

	// 上传文件
	attachment, err := h.attachmentService.UploadFile(taskID, file, uploadedBy)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "文件上传成功",
		"attachment": attachment,
	})
}

// GetTaskAttachments 获取任务附件列表API
func (h *AttachmentHandler) GetTaskAttachments(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	attachments, err := h.attachmentService.GetTaskAttachments(taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取附件列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"attachments": attachments})
}

// DownloadFile 下载文件API
func (h *AttachmentHandler) DownloadFile(c *gin.Context) {
	attachmentID := c.Param("id")
	if attachmentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "附件ID不能为空"})
		return
	}

	filePath, originalName, err := h.attachmentService.GetFileContent(attachmentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	// 设置响应头
	c.Header("Content-Disposition", "attachment; filename="+originalName)
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(filePath)
}

// DeleteAttachment 删除附件API
func (h *AttachmentHandler) DeleteAttachment(c *gin.Context) {
	attachmentID := c.Param("id")
	if attachmentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "附件ID不能为空"})
		return
	}

	err := h.attachmentService.DeleteAttachment(attachmentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除附件失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "附件删除成功"})
}

// PreviewFile 预览文件API（主要用于图片）
func (h *AttachmentHandler) PreviewFile(c *gin.Context) {
	attachmentID := c.Param("id")
	if attachmentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "附件ID不能为空"})
		return
	}

	attachment, err := h.attachmentService.GetAttachmentByID(attachmentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "附件不存在"})
		return
	}

	// 检查是否为图片
	if !attachment.IsImage() {
		c.JSON(http.StatusBadRequest, gin.H{"error": "该文件不支持预览"})
		return
	}

	// 设置正确的Content-Type
	c.Header("Content-Type", attachment.MimeType)

	// 发送文件
	c.File(attachment.FilePath)
}
