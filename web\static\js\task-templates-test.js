// 测试版本的任务模板管理JavaScript功能
let currentEditingTemplate = null;
let allTaskTemplates = [];

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('任务模板管理系统初始化');
    initializeTaskTemplateManagement();
    initQuickTemplateSelector();
    loadTaskTemplates();
});

// 全局函数：打开任务模板管理器
window.openTaskTemplateManager = function() {
    console.log('打开任务模板管理器');
    const modal = document.getElementById('taskTemplateModal');
    const modalContent = document.getElementById('taskTemplateModalContent');
    
    if (modal && modalContent) {
        modal.classList.remove('hidden');
        
        // 添加显示动画
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
        
        // 加载模板列表
        loadTaskTemplates();
        
        console.log('模板管理器已打开');
    } else {
        console.error('找不到模板管理器元素');
    }
};

// 全局函数：关闭任务模板管理器
window.closeTaskTemplateManager = function() {
    console.log('关闭任务模板管理器');
    const modal = document.getElementById('taskTemplateModal');
    const modalContent = document.getElementById('taskTemplateModalContent');
    
    if (modal && modalContent) {
        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');
        
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
    }
};

// 初始化任务模板管理功能
function initializeTaskTemplateManagement() {
    const templateModal = document.getElementById('taskTemplateModal');
    const closeTemplateModal = document.getElementById('closeTaskTemplateModal');
    const closeTemplateModalHeader = document.getElementById('closeTaskTemplateModalHeader');
    const addNewTemplateBtn = document.getElementById('addNewTemplateBtn');

    // 关闭模态框
    if (closeTemplateModal) {
        closeTemplateModal.addEventListener('click', function() {
            console.log('点击关闭按钮');
            closeTaskTemplateManager();
        });
    }

    // 头部关闭按钮
    if (closeTemplateModalHeader) {
        closeTemplateModalHeader.addEventListener('click', function() {
            console.log('点击头部关闭按钮');
            closeTaskTemplateManager();
        });
    }

    // 新建模板按钮
    if (addNewTemplateBtn) {
        addNewTemplateBtn.addEventListener('click', function() {
            console.log('点击新建模板按钮');
            showTemplateEditForm();
        });
    }

    // 点击模态框外部关闭
    if (templateModal) {
        templateModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeTaskTemplateManager();
            }
        });
    }
}

// 加载任务模板列表
function loadTaskTemplates() {
    try {
        console.log('开始加载模板');

        // 从API加载模板数据
        fetch('/api/task-templates')
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);
                if (data.success && data.templates) {
                    console.log('从API加载模板成功，模板数量:', data.templates.length);
                    allTaskTemplates = data.templates;
                    renderTaskTemplatesList();
                    updateQuickTemplateSelector();
                    updateTemplateManagementPermissions();
                } else {
                    console.error('加载模板失败:', data.message || '未知错误');
                    console.error('完整响应数据:', data);
                    // 如果API失败，使用空数组
                    allTaskTemplates = [];
                    renderTaskTemplatesList();
                    updateQuickTemplateSelector();
                    updateTemplateManagementPermissions();
                }
            })
            .catch(error => {
                console.error('加载模板时出错:', error);
                console.error('错误详情:', error.message);
                // 如果网络错误，使用空数组
                allTaskTemplates = [];
                renderTaskTemplatesList();
                updateQuickTemplateSelector();
                updateTemplateManagementPermissions();
            });

        return; // 提前返回，因为现在是异步操作
    } catch (error) {
        console.error('加载模板时出错:', error);
        allTaskTemplates = [];
        renderTaskTemplatesList();
        updateQuickTemplateSelector();
        updateTemplateManagementPermissions();
    }
}




// 批量删除所有系统模板
function deleteAllSystemTemplates() {
    const systemTemplateIds = [
        'default-bug',
        'default-feature',
        'default-testing',
        'default-documentation',
        'default-research',
        'default-improvement'
    ];

    if (!confirm('确定要删除所有系统默认模板吗？此操作不可撤销，将影响所有用户。')) {
        return;
    }

    try {
        // 将所有系统模板ID添加到禁用列表
        let disabledTemplates = JSON.parse(localStorage.getItem('projectm2_disabled_templates') || '[]');

        systemTemplateIds.forEach(templateId => {
            if (!disabledTemplates.includes(templateId)) {
                disabledTemplates.push(templateId);
            }
        });

        localStorage.setItem('projectm2_disabled_templates', JSON.stringify(disabledTemplates));

        // 重新加载模板列表
        loadTaskTemplates();

        alert('所有系统模板已删除成功！');

    } catch (error) {
        console.error('批量删除系统模板失败:', error);
        alert('删除失败：' + error.message);
    }
}

// 将函数暴露到全局作用域
window.deleteAllSystemTemplates = deleteAllSystemTemplates;



// 更新模板管理权限控制
function updateTemplateManagementPermissions() {
    console.log('检查模板管理权限');

    // 暂时允许所有用户管理模板，便于调试
    const hasManagePermission = true; // window.hasPermission && window.hasPermission('manage_templates');

    console.log('模板管理权限:', hasManagePermission);

    // 控制新建模板按钮显示
    const addNewTemplateBtn = document.getElementById('addNewTemplateBtn');
    if (addNewTemplateBtn) {
        console.log('找到新建模板按钮');
        if (hasManagePermission) {
            addNewTemplateBtn.classList.remove('hidden');
            console.log('显示新建模板按钮');
        } else {
            addNewTemplateBtn.classList.add('hidden');
            console.log('隐藏新建模板按钮');
        }
    } else {
        console.log('未找到新建模板按钮元素');
    }

    // 控制快速模板管理按钮显示
    const quickTemplateManageBtn = document.getElementById('quickTemplateManageBtn');
    if (quickTemplateManageBtn) {
        if (hasManagePermission) {
            quickTemplateManageBtn.classList.remove('hidden');
        } else {
            quickTemplateManageBtn.classList.add('hidden');
        }
    }
}

// 渲染任务模板列表
function renderTaskTemplatesList() {
    const container = document.getElementById('taskTemplatesList');
    if (!container) return;

    if (allTaskTemplates.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-16">
                <div class="text-6xl mb-4 opacity-50">📋</div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无模板</h3>
                <p class="text-gray-500">点击"新建模板"创建您的第一个任务模板</p>
            </div>
        `;
        return;
    }

    // 获取优先级和类型的样式
    const getPriorityStyle = (priority) => {
        const styles = {
            'low': 'bg-green-100 text-green-800 border-green-200',
            'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'high': 'bg-red-100 text-red-800 border-red-200'
        };
        return styles[priority] || styles['medium'];
    };

    const getTypeIcon = (type) => {
        const icons = {
            'task': '📋',
            'feature': '🚀',
            'bug': '🐛',
            'improvement': '⚡',
            'research': '🔍',
            'testing': '🧪',
            'documentation': '📚',
            'maintenance': '🔧'
        };
        return icons[type] || '📋';
    };

    const getPriorityText = (priority) => {
        const texts = {
            'low': '低优先级',
            'medium': '中优先级',
            'high': '高优先级'
        };
        console.log('获取优先级文本:', priority, '->', texts[priority] || '中优先级');
        return texts[priority] || '中优先级';
    };

    console.log('渲染模板列表，模板数量:', allTaskTemplates.length);
    allTaskTemplates.forEach(template => {
        console.log('模板:', template.name, '优先级:', template.priority);
    });

    console.log('🎯 渲染模板列表，模板数量:', allTaskTemplates.length);
    allTaskTemplates.forEach(template => {
        console.log('模板详情:', template.name, 'isDefault:', template.isDefault);
    });

    container.innerHTML = allTaskTemplates.map(template => `
        <div class="template-card group relative bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200 rounded-2xl p-6 hover:border-blue-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
            <!-- 模板类型图标 -->
            <div class="absolute top-4 right-4 text-2xl opacity-70 group-hover:opacity-100 transition-opacity">
                ${getTypeIcon(template.type)}
            </div>

            <!-- 🚫 已移除系统标签 - 所有模板都是自定义模板 -->

            <!-- 模板内容 -->
            <div class="mt-8">
                <h4 class="text-lg font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                    ${template.name}
                </h4>
                <p class="text-sm text-gray-600 mb-4 line-clamp-2">
                    ${template.description}
                </p>

                <!-- 优先级标识 -->
                <div class="flex items-center justify-between mb-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getPriorityStyle(template.priority)}">
                        ${getPriorityText(template.priority)}
                    </span>
                    <span class="text-xs text-gray-500">
                        默认标题：${template.title.length > 20 ? template.title.substring(0, 20) + '...' : template.title}
                    </span>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button onclick="useTemplate('${template.id}')" class="flex-1 px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        使用
                    </button>
                    ${(() => {
                        // 使用权限系统检查管理模板权限
                        const hasManagePermission = window.hasPermission && window.hasPermission('manage_templates');
                        if (!hasManagePermission) {
                            return ''; // 没有管理权限的用户不显示编辑和删除按钮
                        }

                        // 管理员对所有模板都有编辑删除权限
                        return `
                            <button onclick="editTemplate('${template.id}')" class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors duration-200" title="编辑模板">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button onclick="deleteTemplate('${template.id}')" class="px-3 py-2 bg-red-100 text-red-700 text-sm rounded-lg hover:bg-red-200 transition-colors duration-200" title="删除模板">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        `;
                    })()}
                </div>
            </div>
        </div>
    `).join('');
}

// 显示模板编辑表单
function showTemplateEditForm(template = null) {
    console.log('显示模板编辑表单', template);
    const form = document.getElementById('templateEditForm');
    
    if (form) {
        form.classList.remove('hidden');
        console.log('表单已显示');
    }

    // 绑定表单事件
    bindTemplateFormEvents();
}

// 绑定模板表单事件
function bindTemplateFormEvents() {
    const templateForm = document.getElementById('taskTemplateForm');
    const cancelBtn = document.getElementById('cancelTemplateEdit');

    console.log('绑定表单事件');

    if (templateForm) {
        // 移除之前的事件监听器
        const newForm = templateForm.cloneNode(true);
        templateForm.parentNode.replaceChild(newForm, templateForm);

        // 重新获取新的表单元素
        const form = document.getElementById('taskTemplateForm');

        // 绑定提交事件
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('表单提交事件触发！');
            saveTaskTemplate();
        });

        console.log('表单提交事件已绑定');
    }

    // 绑定取消按钮事件
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            console.log('取消编辑模板');
            hideTemplateEditForm();
            resetTemplateForm();
        });
    }
}

// 全局函数：使用模板创建任务
window.useTemplate = function(templateId) {
    console.log('使用模板创建任务:', templateId);
    console.log('当前所有模板:', allTaskTemplates);

    const template = allTaskTemplates.find(t => t.id === templateId);
    if (!template) {
        console.error('模板不存在，ID:', templateId);
        alert('模板不存在');
        return;
    }

    console.log('找到的模板:', template);
    console.log('模板内容:', template.content);
    console.log('模板内容长度:', template.content ? template.content.length : 0);

    // 关闭模板管理器
    closeTaskTemplateManager();

    // 打开任务创建表单并填充模板数据
    setTimeout(() => {
        openTaskModal();

        // 填充表单数据，使用重试机制确保富文本编辑器已初始化
        const fillFormData = (retryCount = 0) => {
            const titleInput = document.getElementById('taskTitle');
            const descriptionTextarea = document.getElementById('taskDescription');
            const prioritySelect = document.getElementById('taskPriority');
            const typeSelect = document.getElementById('taskType');

            if (titleInput) titleInput.value = template.title;

            // 处理富文本编辑器内容
            if (window.quillEditor && template.content) {
                console.log('设置富文本编辑器内容:', template.content);
                // 设置HTML内容而不是纯文本
                window.quillEditor.root.innerHTML = template.content;
                console.log('富文本编辑器内容设置成功');
            } else if (descriptionTextarea && template.content) {
                console.log('设置文本框内容:', template.content);
                descriptionTextarea.value = template.content;
            } else if (template.content && retryCount < 3) {
                // 如果富文本编辑器还没初始化，重试
                console.log(`富文本编辑器未就绪，重试 ${retryCount + 1}/3`);
                setTimeout(() => fillFormData(retryCount + 1), 200);
                return;
            }

            // 调试信息
            console.log('模板内容:', template.content);
            console.log('富文本编辑器存在:', !!window.quillEditor);
            console.log('文本框存在:', !!descriptionTextarea);

            if (prioritySelect) prioritySelect.value = template.priority;
            if (typeSelect) typeSelect.value = template.type;

            console.log('模板数据已填充到任务表单');
        };

        setTimeout(fillFormData, 200);
    }, 300);
};

// 调试函数：检查模板数据
window.debugTemplateData = function() {
    console.log('=== 调试模板数据 ===');
    console.log('allTaskTemplates:', allTaskTemplates);

    if (allTaskTemplates && allTaskTemplates.length > 0) {
        allTaskTemplates.forEach((template, index) => {
            console.log(`模板 ${index + 1}:`, {
                id: template.id,
                name: template.name,
                title: template.title,
                content: template.content,
                contentLength: template.content ? template.content.length : 0
            });
        });
    } else {
        console.log('没有找到模板数据');
    }

    console.log('富文本编辑器状态:', !!window.quillEditor);
    console.log('===================');
};

// 全局函数：编辑模板
window.editTemplate = function(templateId) {
    console.log('编辑模板:', templateId);

    const template = allTaskTemplates.find(t => t.id === templateId);
    if (!template) {
        alert('模板不存在');
        return;
    }

    // 管理员可以编辑所有模板，包括系统默认模板
    // 设置当前编辑的模板
    currentEditingTemplate = template;

    // 显示编辑表单
    showTemplateEditForm(template);

    // 填充表单数据
    document.getElementById('templateName').value = template.name;
    document.getElementById('templateDescription').value = template.description || '';
    document.getElementById('templateTitle').value = template.title;
    document.getElementById('templateContent').value = template.content || '';
    document.getElementById('templatePriority').value = template.priority;
    document.getElementById('templateType').value = template.type;
    document.getElementById('editingTemplateId').value = template.id;

    // 更新表单标题
    document.getElementById('templateFormTitle').textContent = '编辑模板';
};

// 全局函数：删除模板
window.deleteTemplate = function(templateId) {
    console.log('删除模板:', templateId);

    const template = allTaskTemplates.find(t => t.id === templateId);
    if (!template) {
        alert('模板不存在');
        return;
    }

    // 确认删除
    if (!confirm(`确定要删除模板"${template.name}"吗？此操作不可撤销。`)) {
        return;
    }

    // 使用API删除模板
    fetch(`/api/task-templates/${templateId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('模板删除成功！');
            loadTaskTemplates(); // 重新加载模板列表
        } else {
            console.error('删除失败:', data.message);
            alert('删除失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('删除模板失败:', error);
        alert('删除失败：网络错误');
    });
};

// 保存任务模板
function saveTaskTemplate() {
    console.log('开始保存模板');

    const name = document.getElementById('templateName').value?.trim();
    const title = document.getElementById('templateTitle').value?.trim();
    const editingId = document.getElementById('editingTemplateId').value;

    console.log('表单数据:', { name, title, editingId });

    // 验证必填字段
    if (!name || !title) {
        alert('请填写模板名称和默认标题');
        return;
    }

    if (name.length > 100) {
        alert('模板名称不能超过100个字符');
        return;
    }

    if (title.length > 255) {
        alert('默认标题不能超过255个字符');
        return;
    }

    const templateData = {
        name: name,
        description: document.getElementById('templateDescription').value?.trim() || '',
        title: title,
        content: document.getElementById('templateContent').value?.trim() || '',
        priority: document.getElementById('templatePriority').value || 'medium',
        type: document.getElementById('templateType').value || 'task'
    };

    console.log('准备发送的模板数据:', templateData);

    // 使用API保存模板
    const url = editingId ? `/api/task-templates/${editingId}` : '/api/task-templates';
    const method = editingId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = editingId ? '模板更新成功！' : '模板保存成功！';
            alert(message);

            hideTemplateEditForm();
            resetTemplateForm();
            loadTaskTemplates(); // 重新加载模板列表
        } else {
            console.error('保存失败:', data.message);
            alert('保存失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        alert('保存失败：网络错误');
    });
}

// 重置模板表单
function resetTemplateForm() {
    document.getElementById('templateName').value = '';
    document.getElementById('templateDescription').value = '';
    document.getElementById('templateTitle').value = '';
    document.getElementById('templateContent').value = '';
    document.getElementById('templatePriority').value = 'medium';
    document.getElementById('templateType').value = 'task';
    document.getElementById('editingTemplateId').value = '';
    document.getElementById('templateFormTitle').textContent = '新建模板';
    currentEditingTemplate = null;
}

// ==================== 快速模板选择器功能 ====================

// 初始化快速模板选择器
function initQuickTemplateSelector() {
    const quickTemplateBtn = document.getElementById('quickTemplateBtn');
    const quickTemplateDropdown = document.getElementById('quickTemplateDropdown');

    if (!quickTemplateBtn || !quickTemplateDropdown) {
        console.log('快速模板选择器元素未找到');
        return;
    }

    // 绑定按钮点击事件
    quickTemplateBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleQuickTemplateDropdown();
    });

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!quickTemplateBtn.contains(e.target) && !quickTemplateDropdown.contains(e.target)) {
            hideQuickTemplateDropdown();
        }
    });

    console.log('快速模板选择器初始化完成');
}

// 显示/隐藏快速模板下拉菜单
function toggleQuickTemplateDropdown() {
    const dropdown = document.getElementById('quickTemplateDropdown');
    if (!dropdown) return;

    if (dropdown.classList.contains('hidden')) {
        showQuickTemplateDropdown();
    } else {
        hideQuickTemplateDropdown();
    }
}

// 显示快速模板下拉菜单
function showQuickTemplateDropdown() {
    const dropdown = document.getElementById('quickTemplateDropdown');
    if (!dropdown) return;

    // 加载模板列表到快速选择器
    loadQuickTemplateList();

    dropdown.classList.remove('hidden');
    setTimeout(() => {
        dropdown.classList.remove('opacity-0', 'scale-95');
        dropdown.classList.add('opacity-100', 'scale-100');
    }, 10);
}

// 隐藏快速模板下拉菜单
function hideQuickTemplateDropdown() {
    const dropdown = document.getElementById('quickTemplateDropdown');
    if (!dropdown) return;

    dropdown.classList.remove('opacity-100', 'scale-100');
    dropdown.classList.add('opacity-0', 'scale-95');

    setTimeout(() => {
        dropdown.classList.add('hidden');
    }, 200);
}

// 加载快速模板列表
function loadQuickTemplateList() {
    const container = document.getElementById('quickTemplateList');
    if (!container) return;

    // 确保模板数据已加载
    if (allTaskTemplates.length === 0) {
        loadTaskTemplates();
    }

    if (allTaskTemplates.length === 0) {
        // 检查当前用户是否有管理模板权限
        const hasManagePermission = window.hasPermission && window.hasPermission('manage_templates');
        const createTemplateButton = hasManagePermission ? `
            <button onclick="openTaskTemplateManager(); hideQuickTemplateDropdown();" class="text-purple-600 hover:text-purple-700 text-sm font-medium mt-2">
                创建第一个模板 →
            </button>
        ` : `
            <p class="text-xs text-gray-400 mt-2">请联系管理员创建模板</p>
        `;

        container.innerHTML = `
            <div class="text-center py-4 text-gray-500">
                <div class="text-2xl mb-2">📋</div>
                <p class="text-sm">暂无可用模板</p>
                ${createTemplateButton}
            </div>
        `;
        return;
    }

    // 获取模板图标
    const getTemplateIcon = (type) => {
        const icons = {
            'task': '📋',
            'feature': '🚀',
            'bug': '🐛',
            'improvement': '⚡',
            'research': '🔍',
            'testing': '🧪',
            'documentation': '📚',
            'maintenance': '🔧'
        };
        return icons[type] || '📋';
    };

    // 渲染模板列表
    container.innerHTML = allTaskTemplates.map(template => `
        <button onclick="quickUseTemplate('${template.id}')" class="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 group">
            <div class="flex items-center space-x-3">
                <div class="text-xl">${getTemplateIcon(template.type)}</div>
                <div class="flex-1 min-w-0">
                    <div class="font-medium text-gray-900 truncate group-hover:text-purple-700">
                        ${template.name}
                    </div>
                    <div class="text-sm text-gray-500 truncate">
                        ${template.description || '无描述'}
                    </div>
                </div>
                <!-- 所有模板都是自定义模板，不需要特殊标识 -->
            </div>
        </button>
    `).join('');
}

// 快速使用模板
window.quickUseTemplate = function(templateId) {
    console.log('快速使用模板:', templateId);
    console.log('当前所有模板:', allTaskTemplates);

    const template = allTaskTemplates.find(t => t.id === templateId);
    if (!template) {
        console.error('模板不存在，ID:', templateId);
        alert('模板不存在');
        return;
    }

    console.log('找到的模板:', template);
    console.log('模板内容:', template.content);
    console.log('模板内容长度:', template.content ? template.content.length : 0);

    // 隐藏下拉菜单
    hideQuickTemplateDropdown();

    // 打开任务创建表单并填充模板数据
    setTimeout(() => {
        // 触发新建任务按钮
        const addTaskBtn = document.getElementById('addTaskBtn');
        if (addTaskBtn) {
            addTaskBtn.click();
        }

        // 填充表单数据，使用重试机制确保富文本编辑器已初始化
        const fillQuickFormData = (retryCount = 0) => {
            const titleInput = document.getElementById('taskTitle');
            const descriptionTextarea = document.getElementById('taskDescription');
            const prioritySelect = document.getElementById('taskPriority');
            const typeSelect = document.getElementById('taskType');

            if (titleInput) titleInput.value = template.title;
            if (prioritySelect) prioritySelect.value = template.priority;
            if (typeSelect) typeSelect.value = template.type;

            // 处理富文本编辑器内容
            if (window.quillEditor && template.content) {
                console.log('快速模板设置富文本编辑器内容:', template.content);
                // 设置HTML内容而不是纯文本
                window.quillEditor.root.innerHTML = template.content;
                console.log('快速模板富文本编辑器内容设置成功');
            } else if (descriptionTextarea && template.content) {
                console.log('快速模板设置文本框内容:', template.content);
                descriptionTextarea.value = template.content;
            } else if (template.content && retryCount < 3) {
                // 如果富文本编辑器还没初始化，重试
                console.log(`快速模板富文本编辑器未就绪，重试 ${retryCount + 1}/3`);
                setTimeout(() => fillQuickFormData(retryCount + 1), 200);
                return;
            }

            // 调试信息
            console.log('快速模板内容:', template.content);
            console.log('富文本编辑器存在:', !!window.quillEditor);
            console.log('文本框存在:', !!descriptionTextarea);

            console.log('快速模板数据已填充到任务表单');

            // 显示成功提示
            showToast(`已应用模板：${template.name}`, 'success');
        };

        setTimeout(fillQuickFormData, 200);
    }, 100);
};

// 在模板数据加载完成后更新快速选择器
function updateQuickTemplateSelector() {
    // 如果快速选择器下拉菜单是打开的，更新其内容
    const dropdown = document.getElementById('quickTemplateDropdown');
    if (dropdown && !dropdown.classList.contains('hidden')) {
        loadQuickTemplateList();
    }
}

// 显示提示消息的辅助函数
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-[10000] px-4 py-2 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

    // 根据类型设置样式
    switch (type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
        toast.classList.add('translate-x-0');
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('translate-x-0');
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 隐藏模板编辑表单
function hideTemplateEditForm() {
    const form = document.getElementById('templateEditForm');
    if (form) {
        form.classList.add('hidden');
    }
}

console.log('任务模板管理脚本已加载');
