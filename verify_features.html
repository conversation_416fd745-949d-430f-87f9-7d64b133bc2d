<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能验证页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">🎯 ProjectM2 功能验证</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 主要功能 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">🚀 主要功能</h2>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>任务管理（CRUD）</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>富文本编辑器</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>时间快捷选择</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>附件管理</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>用户管理</span>
                    </div>
                </div>
            </div>

            <!-- 技术特性 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">⚡ 技术特性</h2>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>RESTful API</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>SQLite数据库</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>响应式设计</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>拖拽排序</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span>批量操作</span>
                    </div>
                </div>
            </div>

            <!-- 快速测试 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">🧪 快速测试</h2>
                <div class="space-y-3">
                    <button onclick="testAPI()" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                        测试API连接
                    </button>
                    <button onclick="testTasks()" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                        测试任务功能
                    </button>
                    <button onclick="testUsers()" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">
                        测试用户功能
                    </button>
                    <button onclick="testTimePresets()" class="w-full bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">
                        测试时间预设
                    </button>
                </div>
            </div>

            <!-- 访问链接 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">🔗 快速访问</h2>
                <div class="space-y-3">
                    <a href="http://localhost:8084/" target="_blank" class="block w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 text-center">
                        📋 项目看板
                    </a>
                    <a href="http://localhost:8084/users" target="_blank" class="block w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 text-center">
                        👥 用户管理
                    </a>
                    <a href="http://localhost:8084/api/tasks" target="_blank" class="block w-full bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700 text-center">
                        🔌 API测试
                    </a>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div id="testResults" class="mt-8 bg-white rounded-lg shadow p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">📊 测试结果</h2>
            <div id="resultContent" class="space-y-2"></div>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const content = document.getElementById('resultContent');
            
            results.classList.remove('hidden');
            
            const div = document.createElement('div');
            div.className = `p-3 rounded ${type === 'success' ? 'bg-green-100 text-green-800' : 
                                         type === 'error' ? 'bg-red-100 text-red-800' : 
                                         'bg-blue-100 text-blue-800'}`;
            div.textContent = message;
            
            content.appendChild(div);
        }

        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8084/api/tasks');
                if (response.ok) {
                    showResult('✅ API连接成功', 'success');
                } else {
                    showResult('❌ API连接失败: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('❌ API连接错误: ' + error.message, 'error');
            }
        }

        async function testTasks() {
            try {
                const response = await fetch('http://localhost:8084/api/tasks');
                const data = await response.json();
                showResult(`✅ 任务功能正常，当前任务数: ${data.tasks.length}`, 'success');
            } catch (error) {
                showResult('❌ 任务功能测试失败: ' + error.message, 'error');
            }
        }

        async function testUsers() {
            try {
                const response = await fetch('http://localhost:8084/api/users');
                const data = await response.json();
                showResult(`✅ 用户功能正常，当前用户数: ${data.users.length}`, 'success');
            } catch (error) {
                showResult('❌ 用户功能测试失败: ' + error.message, 'error');
            }
        }

        async function testTimePresets() {
            try {
                const response = await fetch('http://localhost:8084/api/time/presets');
                const data = await response.json();
                showResult(`✅ 时间预设功能正常，预设数: ${data.presets.length}`, 'success');
            } catch (error) {
                showResult('❌ 时间预设功能测试失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动测试API连接
        window.addEventListener('load', function() {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
