package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TaskTypeModel 任务类型数据模型
type TaskTypeModel struct {
	ID          string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string         `json:"name" gorm:"not null;size:50;uniqueIndex"` // 类型名称（英文标识）
	DisplayName string         `json:"display_name" gorm:"not null;size:100"`    // 显示名称（中文）
	Icon        string         `json:"icon" gorm:"not null;size:10"`             // 图标（emoji）
	Color       string         `json:"color" gorm:"not null;size:20"`            // 颜色代码
	Description string         `json:"description" gorm:"type:text"`             // 描述
	IsSystem    bool           `json:"is_system" gorm:"not null;default:false"`  // 是否为系统预设类型
	IsActive    bool           `json:"is_active" gorm:"not null;default:true"`   // 是否启用
	SortOrder   int            `json:"sort_order" gorm:"not null;default:0"`     // 排序顺序
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate 创建前钩子
func (t *TaskTypeModel) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = uuid.New().String()
	}
	return nil
}

// TableName 指定表名
func (TaskTypeModel) TableName() string {
	return "task_types"
}

// TaskTypeCreateRequest 创建任务类型请求结构
type TaskTypeCreateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Icon        string `json:"icon" binding:"required,min=1,max=10"`
	Color       string `json:"color" binding:"required,min=1,max=20"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
}

// TaskTypeUpdateRequest 更新任务类型请求结构
type TaskTypeUpdateRequest struct {
	DisplayName string `json:"display_name" binding:"min=1,max=100"`
	Icon        string `json:"icon" binding:"min=1,max=10"`
	Color       string `json:"color" binding:"min=1,max=20"`
	Description string `json:"description"`
	IsActive    *bool  `json:"is_active"`
	SortOrder   *int   `json:"sort_order"`
}

// GetDefaultTaskTypes 获取默认的任务类型数据
func GetDefaultTaskTypes() []TaskTypeModel {
	return []TaskTypeModel{
		{
			Name:        "task",
			DisplayName: "一般任务",
			Icon:        "📋",
			Color:       "#6B7280",
			Description: "普通的工作任务",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   1,
		},
		{
			Name:        "feature",
			DisplayName: "功能开发",
			Icon:        "🚀",
			Color:       "#3B82F6",
			Description: "新功能的开发工作",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   2,
		},
		{
			Name:        "bug",
			DisplayName: "缺陷修复",
			Icon:        "🐛",
			Color:       "#EF4444",
			Description: "修复系统缺陷和错误",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   3,
		},
		{
			Name:        "improvement",
			DisplayName: "改进优化",
			Icon:        "⚡",
			Color:       "#F59E0B",
			Description: "性能优化和改进工作",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   4,
		},
		{
			Name:        "research",
			DisplayName: "研究调研",
			Icon:        "🔍",
			Color:       "#8B5CF6",
			Description: "技术调研和研究工作",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   5,
		},
		{
			Name:        "testing",
			DisplayName: "测试",
			Icon:        "🧪",
			Color:       "#10B981",
			Description: "测试相关工作",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   6,
		},
		{
			Name:        "documentation",
			DisplayName: "文档",
			Icon:        "📚",
			Color:       "#06B6D4",
			Description: "文档编写和维护",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   7,
		},
		{
			Name:        "maintenance",
			DisplayName: "维护",
			Icon:        "🔧",
			Color:       "#84CC16",
			Description: "系统维护和运维工作",
			IsSystem:    false, // 改为自定义类型，允许管理员编辑删除
			IsActive:    true,
			SortOrder:   8,
		},
	}
}
