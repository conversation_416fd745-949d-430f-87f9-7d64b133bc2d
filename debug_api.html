<!DOCTYPE html>
<html>
<head>
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>API调试页面</h1>
    
    <div class="debug-section">
        <h2>1. 获取所有任务</h2>
        <button onclick="getAllTasks()">获取所有任务</button>
        <pre id="allTasksResult"></pre>
    </div>
    
    <div class="debug-section">
        <h2>2. 获取单个任务详情</h2>
        <input type="text" id="taskId" placeholder="输入任务ID" style="width: 300px;">
        <button onclick="getTaskById()">获取任务详情</button>
        <pre id="taskDetailResult"></pre>
    </div>
    
    <div class="debug-section">
        <h2>3. 测试表单字段设置</h2>
        <button onclick="testFormFields()">测试表单字段</button>
        <div style="margin-top: 10px;">
            <label>分配给: <select id="testAssignee"><option value="">未分配</option><option value="test">测试用户</option></select></label><br><br>
            <label>开始时间: <input type="date" id="testStartDate"></label><br><br>
            <label>结束时间: <input type="date" id="testEndDate"></label><br><br>
            <label>截止日期: <input type="date" id="testDueDate"></label><br><br>
            <label>预估工时: <input type="number" id="testEstimatedHours"></label><br><br>
            <label>实际工时: <input type="number" id="testActualHours"></label><br><br>
            <label>标签: <input type="text" id="testTags"></label>
        </div>
        <pre id="formTestResult"></pre>
    </div>

    <script>
        async function getAllTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();
                document.getElementById('allTasksResult').textContent = JSON.stringify(data, null, 2);
                console.log('所有任务数据:', data);
            } catch (error) {
                document.getElementById('allTasksResult').textContent = '错误: ' + error.message;
                console.error('获取任务失败:', error);
            }
        }

        async function getTaskById() {
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                alert('请输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`/api/tasks/${taskId}`);
                const data = await response.json();
                document.getElementById('taskDetailResult').textContent = JSON.stringify(data, null, 2);
                console.log('任务详情数据:', data);
                
                // 如果获取成功，自动测试表单字段设置
                if (data.task) {
                    testFormFieldsWithData(data.task);
                }
            } catch (error) {
                document.getElementById('taskDetailResult').textContent = '错误: ' + error.message;
                console.error('获取任务详情失败:', error);
            }
        }

        function testFormFields() {
            const testData = {
                assigned_to: 'test',
                start_date: '2025-01-01T00:00:00Z',
                end_date: '2025-01-07T00:00:00Z',
                due_date: '2025-01-10T00:00:00Z',
                estimated_hours: 16,
                actual_hours: 8,
                tags: '["测试", "调试", "前端"]'
            };
            
            testFormFieldsWithData(testData);
        }

        function testFormFieldsWithData(task) {
            const results = [];
            
            // 测试分配用户
            const assigneeSelect = document.getElementById('testAssignee');
            if (assigneeSelect && task.assigned_to) {
                assigneeSelect.value = task.assigned_to;
                results.push(`分配用户设置: ${task.assigned_to} -> ${assigneeSelect.value}`);
            }
            
            // 测试开始日期
            const startDateInput = document.getElementById('testStartDate');
            if (startDateInput && task.start_date) {
                const startDate = new Date(task.start_date);
                if (!isNaN(startDate.getTime())) {
                    const dateValue = startDate.toISOString().slice(0, 10);
                    startDateInput.value = dateValue;
                    results.push(`开始日期设置: ${task.start_date} -> ${dateValue} -> ${startDateInput.value}`);
                } else {
                    results.push(`开始日期格式无效: ${task.start_date}`);
                }
            }
            
            // 测试结束日期
            const endDateInput = document.getElementById('testEndDate');
            if (endDateInput && task.end_date) {
                const endDate = new Date(task.end_date);
                if (!isNaN(endDate.getTime())) {
                    const dateValue = endDate.toISOString().slice(0, 10);
                    endDateInput.value = dateValue;
                    results.push(`结束日期设置: ${task.end_date} -> ${dateValue} -> ${endDateInput.value}`);
                } else {
                    results.push(`结束日期格式无效: ${task.end_date}`);
                }
            }
            
            // 测试截止日期
            const dueDateInput = document.getElementById('testDueDate');
            if (dueDateInput && task.due_date) {
                const dueDate = new Date(task.due_date);
                if (!isNaN(dueDate.getTime())) {
                    const dateValue = dueDate.toISOString().slice(0, 10);
                    dueDateInput.value = dateValue;
                    results.push(`截止日期设置: ${task.due_date} -> ${dateValue} -> ${dueDateInput.value}`);
                } else {
                    results.push(`截止日期格式无效: ${task.due_date}`);
                }
            }
            
            // 测试预估工时
            const estimatedHoursInput = document.getElementById('testEstimatedHours');
            if (estimatedHoursInput && task.estimated_hours !== undefined) {
                estimatedHoursInput.value = task.estimated_hours || 0;
                results.push(`预估工时设置: ${task.estimated_hours} -> ${estimatedHoursInput.value}`);
            }
            
            // 测试实际工时
            const actualHoursInput = document.getElementById('testActualHours');
            if (actualHoursInput && task.actual_hours !== undefined) {
                actualHoursInput.value = task.actual_hours || 0;
                results.push(`实际工时设置: ${task.actual_hours} -> ${actualHoursInput.value}`);
            }
            
            // 测试标签
            const tagsInput = document.getElementById('testTags');
            if (tagsInput && task.tags) {
                let tagsArray = [];
                if (typeof task.tags === 'string') {
                    try {
                        tagsArray = JSON.parse(task.tags);
                    } catch (e) {
                        tagsArray = task.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                    }
                } else if (Array.isArray(task.tags)) {
                    tagsArray = task.tags;
                }
                tagsInput.value = tagsArray.join(', ');
                results.push(`标签设置: ${task.tags} -> ${tagsArray.join(', ')} -> ${tagsInput.value}`);
            }
            
            document.getElementById('formTestResult').textContent = results.join('\n');
            console.log('表单字段测试结果:', results);
        }

        // 页面加载时自动获取所有任务
        window.onload = function() {
            getAllTasks();
        };
    </script>
</body>
</html>
