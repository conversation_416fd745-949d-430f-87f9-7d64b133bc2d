# 项目管理看板系统 🚀

一个现代化的项目管理看板系统，采用 Golang + HTML模板 + SQLite 技术栈构建，达到国际产品水准。

## ✨ 特性

### 🎯 核心功能
- **看板视图**: 直观的三列布局（待办、进行中、已完成）
- **拖拽操作**: 流畅的任务卡片拖拽体验
- **任务管理**: 完整的CRUD操作（创建、读取、更新、删除）
- **优先级管理**: 三级优先级系统（高、中、低）
- **实时同步**: 拖拽状态实时保存到数据库

### 🎨 用户体验
- **响应式设计**: 完美适配桌面和移动设备
- **暗色主题**: 支持明暗主题切换
- **搜索过滤**: 强大的任务搜索和优先级过滤
- **快捷键支持**: 提高操作效率
- **右键菜单**: 快捷操作菜单
- **动画效果**: 流畅的交互动画

### 🔧 技术特性
- **高性能**: Gin框架提供卓越性能
- **轻量级**: SQLite数据库，无需额外配置
- **现代化UI**: TailwindCSS + 自定义样式
- **无刷新交互**: SortableJS + 原生JavaScript
- **类型安全**: 完整的数据模型定义

## 🛠️ 技术栈

- **后端**: Go 1.21+ / Gin Framework / GORM / SQLite
- **前端**: HTML5 / TailwindCSS / JavaScript ES6+ / SortableJS
- **数据库**: SQLite 3
- **工具**: UUID生成 / 响应式设计 / PWA就绪

## 🚀 快速开始

### 环境要求
- Go 1.21 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装运行

1. **克隆项目**
```bash
git clone <repository-url>
cd projectm2
```

2. **安装依赖**
```bash
go mod tidy
```

3. **启动服务**
```bash
go run main.go
```

4. **访问应用**
打开浏览器访问: http://localhost:8080

## 📖 使用指南

### 基本操作
- **新建任务**: 点击右上角"新建任务"按钮或使用快捷键 `Ctrl+N`
- **编辑任务**: 点击任务卡片上的编辑图标
- **删除任务**: 点击任务卡片上的删除图标
- **移动任务**: 直接拖拽任务卡片到不同状态列

### 快捷键
- `Ctrl + N`: 新建任务
- `Ctrl + /`: 显示帮助
- `Esc`: 关闭模态框
- 右键点击任务: 显示快捷菜单

### 搜索和过滤
- 使用顶部搜索框按标题或描述搜索任务
- 使用优先级过滤器筛选特定优先级的任务
- 支持实时搜索，无需按回车

## 🏗️ 项目结构

```
projectm2/
├── main.go                 # 应用入口
├── internal/
│   ├── models/            # 数据模型
│   ├── handlers/          # HTTP处理器
│   ├── services/          # 业务逻辑
│   └── database/          # 数据库配置
├── web/
│   ├── templates/         # HTML模板
│   └── static/           # 静态资源
│       ├── css/          # 样式文件
│       └── js/           # JavaScript文件
├── go.mod                # Go模块文件
└── README.md            # 项目说明
```

## 🔌 API 接口

### 任务管理
- `GET /api/tasks` - 获取所有任务
- `POST /api/tasks` - 创建新任务
- `PUT /api/tasks/:id` - 更新任务信息
- `DELETE /api/tasks/:id` - 删除任务
- `PUT /api/tasks/:id/status` - 更新任务状态
- `PUT /api/tasks/:id/position` - 更新任务位置

### 页面路由
- `GET /` - 主页看板视图

## 🎨 自定义配置

### 主题定制
项目支持明暗主题切换，可在 `web/static/css/style.css` 中自定义主题颜色。

### 数据库配置
默认使用SQLite数据库，文件位于项目根目录的 `projectm2.db`。

## 🧪 测试

项目包含API测试脚本，可验证所有核心功能：

```bash
cd test_dir
go run test_api.go
```

## 📈 性能优化

- 使用防抖技术优化搜索性能
- 实现虚拟滚动支持大量任务
- 采用事务确保数据一致性
- 优化SQL查询减少数据库负载

## 🔒 安全特性

- 输入验证和清理
- SQL注入防护（GORM ORM）
- XSS防护
- CSRF保护就绪

## 🌟 未来规划

- [ ] 用户认证和权限管理
- [ ] 团队协作功能
- [ ] 任务评论和附件
- [ ] 数据导出功能
- [ ] 移动端APP
- [ ] 实时通知
- [ ] 甘特图视图
- [ ] 时间跟踪

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License

---

**项目管理看板系统** - 让项目管理变得简单高效 ✨
