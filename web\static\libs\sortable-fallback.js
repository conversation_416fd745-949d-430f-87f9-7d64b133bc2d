/**
 * 简化版拖拽排序功能 - SortableJS 的轻量级替代方案
 * 当CDN无法访问时使用此备用方案
 */

(function(global) {
    'use strict';

    // 简化版 Sortable 构造函数
    function Sortable(element, options) {
        this.el = element;
        this.options = options || {};
        this.init();
    }

    Sortable.prototype = {
        init: function() {
            if (!this.el) return;
            
            this.el.style.position = 'relative';
            this.bindEvents();
        },

        bindEvents: function() {
            var self = this;
            var draggedElement = null;
            var placeholder = null;

            // 为所有子元素添加拖拽属性
            this.updateDraggableItems();

            this.el.addEventListener('dragstart', function(e) {
                draggedElement = e.target.closest('[draggable="true"]');
                if (!draggedElement) return;

                // 创建占位符
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.style.height = draggedElement.offsetHeight + 'px';
                placeholder.style.backgroundColor = '#f3f4f6';
                placeholder.style.border = '2px dashed #d1d5db';
                placeholder.style.borderRadius = '8px';
                placeholder.style.margin = '4px 0';

                draggedElement.style.opacity = '0.5';
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggedElement.outerHTML);
            });

            this.el.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                var afterElement = self.getDragAfterElement(e.clientY);
                if (afterElement == null) {
                    self.el.appendChild(placeholder);
                } else {
                    self.el.insertBefore(placeholder, afterElement);
                }
            });

            this.el.addEventListener('dragenter', function(e) {
                e.preventDefault();
            });

            this.el.addEventListener('drop', function(e) {
                e.preventDefault();
                if (!draggedElement) return;

                // 移动元素到新位置
                if (placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.remove();
                }

                draggedElement.style.opacity = '';
                
                // 触发回调
                if (self.options.onEnd) {
                    self.options.onEnd({
                        item: draggedElement,
                        oldIndex: -1, // 简化版不计算索引
                        newIndex: -1
                    });
                }

                draggedElement = null;
            });

            this.el.addEventListener('dragend', function(e) {
                if (draggedElement) {
                    draggedElement.style.opacity = '';
                }
                if (placeholder && placeholder.parentNode) {
                    placeholder.remove();
                }
                draggedElement = null;
            });
        },

        getDragAfterElement: function(y) {
            var draggableElements = Array.from(this.el.querySelectorAll('[draggable="true"]:not(.sortable-placeholder)'));
            
            return draggableElements.reduce(function(closest, child) {
                var box = child.getBoundingClientRect();
                var offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        },

        updateDraggableItems: function() {
            var items = this.el.children;
            for (var i = 0; i < items.length; i++) {
                if (!items[i].hasAttribute('draggable')) {
                    items[i].setAttribute('draggable', 'true');
                    items[i].style.cursor = 'move';
                }
            }
        },

        destroy: function() {
            // 简化版销毁方法
            var items = this.el.children;
            for (var i = 0; i < items.length; i++) {
                items[i].removeAttribute('draggable');
                items[i].style.cursor = '';
            }
        }
    };

    // 静态方法
    Sortable.create = function(element, options) {
        return new Sortable(element, options);
    };

    // 导出到全局
    global.Sortable = Sortable;

    // 添加控制台提示
    console.log('🔄 使用简化版拖拽功能 (SortableJS 备用方案)');

})(window);
