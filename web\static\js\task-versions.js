// 任务版本管理JavaScript功能

// ==================== 全局变量 ====================
let allTaskVersions = [];
let currentEditingVersion = null;
let selectedVersionId = null;
let currentVersionFilter = 'all';

// ==================== 全局函数定义（确保在页面加载时就可用） ====================

// 快速选择图标函数
window.quickSelectVersionIcon = function(icon) {
    console.log('🎯 快速选择版本图标:', icon);

    const iconInput = document.getElementById('versionIcon');
    const iconPreview = document.getElementById('versionIconPreview');

    if (iconInput) {
        iconInput.value = icon;
        console.log('✅ 版本图标输入框已更新:', icon);
    }

    if (iconPreview) {
        iconPreview.textContent = icon;
        console.log('✅ 版本图标预览已更新:', icon);
    }

    // 关闭选择器
    const picker = document.getElementById('simpleVersionIconPicker');
    const overlay = document.getElementById('versionPickerOverlay');
    if (picker) picker.remove();
    if (overlay) overlay.remove();

    console.log('✅ 版本图标选择完成，选择器已关闭');
};

// 打开任务版本管理器
window.openTaskVersionManager = function() {
    console.log('打开任务版本管理器');
    const modal = document.getElementById('taskVersionModal');
    const modalContent = document.getElementById('taskVersionModalContent');
    if (modal && modalContent) {
        modal.classList.remove('hidden');
        // 触发动画
        setTimeout(() => {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }, 10);
        loadTaskVersions();
        hideVersionEditForm();
    }
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTaskVersionManagement();
    // 初始化时加载任务版本，用于更新任务表单选项
    loadTaskVersionsForForm();
});

// 初始化任务版本管理功能
function initializeTaskVersionManagement() {
    const taskVersionModal = document.getElementById('taskVersionModal');
    const closeTaskVersionModal = document.getElementById('closeTaskVersionModal');
    const closeTaskVersionModalHeader = document.getElementById('closeTaskVersionModalHeader');
    const addNewVersionBtn = document.getElementById('addNewVersionBtn');
    const cancelVersionEdit = document.getElementById('cancelVersionEdit');
    const cancelVersionEditBtn = document.getElementById('cancelVersionEditBtn');
    const taskVersionForm = document.getElementById('taskVersionForm');
    const versionIcon = document.getElementById('versionIcon');
    const versionIconPreview = document.getElementById('versionIconPreview');
    const versionColor = document.getElementById('versionColor');
    const versionColorText = document.getElementById('versionColorText');

    // 关闭模态框
    if (closeTaskVersionModal) {
        closeTaskVersionModal.addEventListener('click', function() {
            closeTaskVersionManager();
        });
    }

    // 头部关闭按钮
    if (closeTaskVersionModalHeader) {
        closeTaskVersionModalHeader.addEventListener('click', function() {
            closeTaskVersionManager();
        });
    }

    // 新建版本按钮
    if (addNewVersionBtn) {
        addNewVersionBtn.addEventListener('click', function() {
            showVersionEditForm();
        });
    }

    // 取消编辑按钮
    if (cancelVersionEdit) {
        cancelVersionEdit.addEventListener('click', function() {
            hideVersionEditForm();
        });
    }

    if (cancelVersionEditBtn) {
        cancelVersionEditBtn.addEventListener('click', function() {
            hideVersionEditForm();
        });
    }

    // 表单提交
    if (taskVersionForm) {
        taskVersionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveTaskVersion();
        });
    }

    // 图标预览
    if (versionIcon && versionIconPreview) {
        versionIcon.addEventListener('input', function() {
            versionIconPreview.textContent = this.value || '📋';
        });
    }

    // 颜色同步
    if (versionColor && versionColorText) {
        versionColor.addEventListener('input', function() {
            versionColorText.value = this.value;
        });

        versionColorText.addEventListener('input', function() {
            if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                versionColor.value = this.value;
            }
        });
    }

    // 筛选功能
    const filterButtons = ['filterAllVersions', 'filterActiveVersions', 'filterInactiveVersions'];
    filterButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', function() {
                // 更新筛选状态
                filterButtons.forEach(id => {
                    const btn = document.getElementById(id);
                    if (btn) {
                        btn.classList.remove('bg-blue-100', 'text-blue-700');
                        btn.classList.add('bg-gray-100', 'text-gray-600');
                    }
                });

                this.classList.remove('bg-gray-100', 'text-gray-600');
                this.classList.add('bg-blue-100', 'text-blue-700');

                // 设置筛选条件
                switch(buttonId) {
                    case 'filterAllVersions':
                        currentVersionFilter = 'all';
                        break;
                    case 'filterActiveVersions':
                        currentVersionFilter = 'active';
                        break;
                    case 'filterInactiveVersions':
                        currentVersionFilter = 'inactive';
                        break;
                }

                renderTaskVersionsList();
            });
        }
    });

    // 点击模态框外部关闭
    if (taskVersionModal) {
        taskVersionModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeTaskVersionManager();
            }
        });
    }

    // 使用事件委托处理图标选择器
    document.addEventListener('click', function(e) {
        // 处理版本图标选择器切换
        const toggleButton = e.target.closest('[data-action="toggle-version-icon-picker"]');
        if (toggleButton) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 版本图标选择器按钮被点击');

            // 直接创建简单图标选择器
            createSimpleVersionIconPicker();
            return;
        }

        // 点击外部关闭图标选择器
        const iconPicker = document.getElementById('versionIconPicker');
        if (iconPicker && !iconPicker.contains(e.target) &&
            !e.target.closest('[data-action="toggle-version-icon-picker"]')) {
            iconPicker.classList.add('hidden');
        }
    });
}

// 关闭任务版本管理器
function closeTaskVersionManager() {
    const modal = document.getElementById('taskVersionModal');
    const modalContent = document.getElementById('taskVersionModalContent');
    if (modal && modalContent) {
        // 退出动画
        modalContent.style.transform = 'scale(0.95)';
        modalContent.style.opacity = '0';
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
        hideVersionEditForm();
        currentEditingVersion = null;
    }
}

// 加载任务版本列表
function loadTaskVersions() {
    fetch('/api/task-versions')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.task_versions) {
                allTaskVersions = data.task_versions;
                renderTaskVersionsList();
            }
        })
        .catch(error => {
            console.error('加载任务版本失败:', error);
            safeShowToast('加载任务版本失败', 'error');
        });
}

// 渲染任务版本列表
function renderTaskVersionsList() {
    const container = document.getElementById('taskVersionsList');
    const countElement = document.getElementById('versionCount');
    
    if (!container) return;

    // 筛选版本
    let filteredVersions = allTaskVersions;
    switch(currentVersionFilter) {
        case 'active':
            filteredVersions = allTaskVersions.filter(v => v.is_active);
            break;
        case 'inactive':
            filteredVersions = allTaskVersions.filter(v => !v.is_active);
            break;
        default:
            filteredVersions = allTaskVersions;
    }

    // 更新计数
    if (countElement) {
        countElement.textContent = filteredVersions.length;
    }

    if (filteredVersions.length === 0) {
        container.innerHTML = getEmptyVersionsHTML();
        return;
    }

    container.innerHTML = filteredVersions.map(version => `
        <div class="group bg-white rounded-xl border border-gray-200 hover:border-purple-300 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer" onclick="selectTaskVersion('${version.id}')">
            <!-- 顶部装饰条 -->
            <div class="h-2 rounded-t-xl" style="background: linear-gradient(90deg, ${version.color}, ${version.color}80)"></div>
            
            <div class="p-4">
                <!-- 版本头部信息 -->
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center text-lg shadow-sm" style="background: ${version.color}20; color: ${version.color};">
                            ${version.icon}
                        </div>
                        <div>
                            <h6 class="font-semibold text-gray-900 text-sm">${version.display_name}</h6>
                            <p class="text-xs text-gray-500">${version.name}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        ${version.is_active ? 
                            '<span class="w-2 h-2 bg-green-500 rounded-full"></span>' : 
                            '<span class="w-2 h-2 bg-gray-400 rounded-full"></span>'
                        }
                        <span class="text-xs ${version.is_active ? 'text-green-600' : 'text-gray-500'}">${version.is_active ? '激活' : '停用'}</span>
                    </div>
                </div>

                <!-- 版本描述 -->
                ${version.description ? `
                    <div class="bg-gray-50 rounded-lg p-2 mb-3">
                        <p class="text-xs text-gray-600 line-clamp-2">${version.description}</p>
                    </div>
                ` : ''}

                <!-- 操作按钮 -->
                <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" style="background-color: ${version.color}"></div>
                        <span class="text-xs text-gray-400">${version.color}</span>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="event.stopPropagation(); editTaskVersion('${version.id}')"
                                class="px-2 py-1 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded text-xs transition-colors">
                            编辑
                        </button>
                        <button onclick="event.stopPropagation(); deleteTaskVersion('${version.id}')"
                                class="px-2 py-1 bg-red-50 hover:bg-red-100 text-red-600 rounded text-xs transition-colors">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 获取空版本列表HTML
function getEmptyVersionsHTML() {
    return `
        <div class="col-span-full text-center py-12">
            <div class="w-24 h-24 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <svg class="w-12 h-12 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
            </div>

            <div class="space-y-4">
                <button onclick="document.getElementById('addNewVersionBtn').click()"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-300 font-semibold inline-flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    创建第一个版本
                </button>
                <p class="text-gray-500 text-sm">还没有任何版本，创建第一个版本来开始管理项目版本</p>
            </div>
        </div>
    `;
}

// 选择任务版本
function selectTaskVersion(versionId) {
    selectedVersionId = versionId;
    const version = allTaskVersions.find(v => v.id === versionId);
    if (version) {
        // 更新选中状态
        updateSelectedVersionUI(versionId);
        // 显示编辑表单
        showVersionEditForm(version);
    }
}

// 更新选中状态的UI
function updateSelectedVersionUI(versionId) {
    // 移除所有选中状态
    document.querySelectorAll('#taskVersionsList .group').forEach(card => {
        card.classList.remove('border-purple-500', 'bg-purple-50');
        card.classList.add('border-gray-200');
    });

    // 添加选中状态
    const selectedCard = document.querySelector(`#taskVersionsList .group[onclick="selectTaskVersion('${versionId}')"]`);
    if (selectedCard) {
        selectedCard.classList.remove('border-gray-200');
        selectedCard.classList.add('border-purple-500', 'bg-purple-50');
    }
}

// 显示版本编辑表单
function showVersionEditForm(version = null) {
    const form = document.getElementById('versionEditForm');
    const placeholder = document.getElementById('versionEditPlaceholder');
    const title = document.getElementById('versionFormTitle');

    // 隐藏占位符，显示表单
    if (placeholder) {
        placeholder.classList.add('hidden');
    }
    if (form) {
        form.classList.remove('hidden');
    }

    if (version) {
        // 编辑模式
        currentEditingVersion = version;
        selectedVersionId = version.id;
        if (title) title.textContent = '编辑版本';

        document.getElementById('editingVersionId').value = version.id;
        document.getElementById('versionName').value = version.name;
        document.getElementById('versionDisplayName').value = version.display_name;
        document.getElementById('versionIcon').value = version.icon;
        document.getElementById('versionColor').value = version.color;
        document.getElementById('versionColorText').value = version.color;
        document.getElementById('versionDescription').value = version.description || '';
        document.getElementById('versionIconPreview').textContent = version.icon;

        // 更新选中状态
        updateSelectedVersionUI(version.id);
    } else {
        // 新建模式
        currentEditingVersion = null;
        selectedVersionId = null;
        if (title) title.textContent = '新建版本';

        document.getElementById('taskVersionForm').reset();
        document.getElementById('editingVersionId').value = '';
        document.getElementById('versionColor').value = '#3B82F6';
        document.getElementById('versionColorText').value = '#3B82F6';
        document.getElementById('versionIconPreview').textContent = '📋';

        // 清除选中状态
        document.querySelectorAll('#taskVersionsList .group').forEach(card => {
            card.classList.remove('border-purple-500', 'bg-purple-50');
            card.classList.add('border-gray-200');
        });
    }
}

// 隐藏版本编辑表单
function hideVersionEditForm() {
    const form = document.getElementById('versionEditForm');
    const placeholder = document.getElementById('versionEditPlaceholder');

    if (form) {
        form.classList.add('hidden');
    }
    if (placeholder) {
        placeholder.classList.remove('hidden');
    }

    // 清除选中状态
    document.querySelectorAll('#taskVersionsList .group').forEach(card => {
        card.classList.remove('border-purple-500', 'bg-purple-50');
        card.classList.add('border-gray-200');
    });

    currentEditingVersion = null;
    selectedVersionId = null;
}

// 编辑任务版本
function editTaskVersion(versionId) {
    const version = allTaskVersions.find(v => v.id === versionId);
    if (version) {
        showVersionEditForm(version);
    }
}

// 保存任务版本
function saveTaskVersion() {
    const formData = new FormData(document.getElementById('taskVersionForm'));
    const versionData = {
        name: formData.get('name'),
        display_name: formData.get('display_name'),
        icon: formData.get('icon'),
        color: formData.get('color'),
        description: formData.get('description') || '',
        sort_order: 0
    };

    const versionId = document.getElementById('editingVersionId').value;
    const isEdit = versionId !== '';
    const url = isEdit ? `/api/task-versions/${versionId}` : '/api/task-versions';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(versionData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            safeShowToast(isEdit ? '版本更新成功' : '版本创建成功', 'success');
            hideVersionEditForm();
            loadTaskVersions();
            // 更新任务表单中的版本选项
            updateTaskFormVersionOptions();
        } else {
            safeShowToast(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        safeShowToast('保存失败，请稍后重试', 'error');
    });
}

// 删除任务版本
function deleteTaskVersion(versionId) {
    const version = allTaskVersions.find(v => v.id === versionId);
    if (!version) return;

    if (confirm(`确定要删除版本"${version.display_name}"吗？\n\n注意：删除后无法恢复，但不会影响已关联的任务。`)) {
        fetch(`/api/task-versions/${versionId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                safeShowToast('版本删除成功', 'success');
                loadTaskVersions();
                hideVersionEditForm();
                // 更新任务表单中的版本选项
                updateTaskFormVersionOptions();
            } else {
                safeShowToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            safeShowToast('删除失败，请稍后重试', 'error');
        });
    }
}

// 加载任务版本用于表单初始化
function loadTaskVersionsForForm() {
    fetch('/api/task-versions/active')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.task_versions) {
                allTaskVersions = data.task_versions;
                updateTaskFormVersionOptions();
            }
        })
        .catch(error => {
            console.error('加载任务版本失败:', error);
        });
}

// 更新任务表单中的版本选项
function updateTaskFormVersionOptions() {
    const taskVersionSelect = document.getElementById('taskVersion');
    if (!taskVersionSelect) return;

    // 保存当前选中的值
    const currentValue = taskVersionSelect.value;

    // 清空现有选项
    taskVersionSelect.innerHTML = '<option value="">未指定版本</option>';

    // 添加新选项
    allTaskVersions.forEach(version => {
        if (version.is_active) {
            const option = document.createElement('option');
            option.value = version.id;
            option.textContent = `${version.icon} ${version.display_name}`;
            taskVersionSelect.appendChild(option);
        }
    });

    // 恢复选中的值（如果还存在）
    if (currentValue && Array.from(taskVersionSelect.options).some(opt => opt.value === currentValue)) {
        taskVersionSelect.value = currentValue;
    }
}

// 创建简单版本图标选择器
function createSimpleVersionIconPicker() {
    // 移除现有的选择器
    const existingPicker = document.getElementById('simpleVersionIconPicker');
    const existingOverlay = document.getElementById('versionPickerOverlay');
    if (existingPicker) existingPicker.remove();
    if (existingOverlay) existingOverlay.remove();

    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.id = 'versionPickerOverlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center p-4';

    // 创建选择器容器
    const picker = document.createElement('div');
    picker.id = 'simpleVersionIconPicker';
    picker.className = 'bg-white rounded-xl shadow-2xl p-6 max-w-md w-full max-h-[80vh] overflow-y-auto';

    // 版本相关图标
    const versionIcons = [
        '📋', '🏷️', '📌', '🔖', '📝', '📄', '📊', '📈',
        '🎯', '🚀', '⭐', '🔥', '💎', '🎨', '🔧', '⚙️',
        '📦', '🎁', '📮', '📫', '📬', '📭', '📯', '📢',
        '🔔', '🔕', '📣', '📡', '🎪', '🎭', '🎨', '🎬'
    ];

    picker.innerHTML = `
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">选择版本图标</h3>
            <button onclick="document.getElementById('versionPickerOverlay').remove()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="grid grid-cols-8 gap-2">
            ${versionIcons.map(icon => `
                <button onclick="quickSelectVersionIcon('${icon}')"
                        class="w-10 h-10 flex items-center justify-center text-lg hover:bg-purple-100 rounded-lg transition-colors">
                    ${icon}
                </button>
            `).join('')}
        </div>
    `;

    overlay.appendChild(picker);
    document.body.appendChild(overlay);

    // 点击遮罩层关闭
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            overlay.remove();
        }
    });
}

// 安全显示Toast消息的函数
function safeShowToast(message, type = 'info') {
    if (typeof showToast === 'function') {
        showToast(message, type);
    } else {
        console.log(`Toast (${type}): ${message}`);
        alert(message);
    }
}
