package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TaskTemplate 任务模板模型
type TaskTemplate struct {
	ID          string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string         `json:"name" gorm:"not null;size:100"`                    // 模板名称
	Description string         `json:"description" gorm:"type:text"`                     // 模板描述
	Title       string         `json:"title" gorm:"not null;size:255"`                  // 默认任务标题
	Content     string         `json:"content" gorm:"type:text"`                        // 模板内容（富文本）
	Priority    TaskPriority   `json:"priority" gorm:"not null;default:'medium'"`       // 默认优先级
	Type        TaskType       `json:"type" gorm:"not null;default:'task'"`             // 默认任务类型
	IsDefault   bool           `json:"is_default" gorm:"not null;default:false"`        // 是否为系统默认模板
	IsActive    bool           `json:"is_active" gorm:"not null;default:true"`          // 是否启用
	SortOrder   int            `json:"sort_order" gorm:"not null;default:0"`            // 排序顺序
	CreatedBy   *string        `json:"created_by" gorm:"type:varchar(36);index"`        // 创建者ID
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator *User `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// BeforeCreate GORM钩子，创建前生成UUID
func (tt *TaskTemplate) BeforeCreate(tx *gorm.DB) error {
	if tt.ID == "" {
		tt.ID = uuid.New().String()
	}
	return nil
}

// TaskTemplateCreateRequest 创建任务模板请求结构
type TaskTemplateCreateRequest struct {
	Name        string       `json:"name" binding:"required,min=1,max=100"`
	Description string       `json:"description"`
	Title       string       `json:"title" binding:"required,min=1,max=255"`
	Content     string       `json:"content"`
	Priority    TaskPriority `json:"priority"`
	Type        TaskType     `json:"type"`
	SortOrder   int          `json:"sort_order"`
}

// TaskTemplateUpdateRequest 更新任务模板请求结构
type TaskTemplateUpdateRequest struct {
	Name        string       `json:"name" binding:"required,min=1,max=100"`
	Description string       `json:"description"`
	Title       string       `json:"title" binding:"required,min=1,max=255"`
	Content     string       `json:"content"`
	Priority    TaskPriority `json:"priority"`
	Type        TaskType     `json:"type"`
	SortOrder   int          `json:"sort_order"`
	IsActive    *bool        `json:"is_active"`
}

// TaskTemplateResponse 任务模板响应结构
type TaskTemplateResponse struct {
	ID          string       `json:"id"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Title       string       `json:"title"`
	Content     string       `json:"content"`
	Priority    TaskPriority `json:"priority"`
	Type        TaskType     `json:"type"`
	IsDefault   bool         `json:"is_default"`
	IsActive    bool         `json:"is_active"`
	SortOrder   int          `json:"sort_order"`
	CreatedBy   *string      `json:"created_by"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Creator     *User        `json:"creator,omitempty"`
}
