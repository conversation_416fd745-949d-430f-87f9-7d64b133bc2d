package services

import (
	"crypto/sha256"
	"fmt"
	"projectm2/internal/models"
	"time"

	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db          *gorm.DB
	userService *UserService
}

// NewAuthService 创建新的认证服务实例
func NewAuthService(db *gorm.DB) *AuthService {
	return &AuthService{
		db:          db,
		userService: NewUserService(db),
	}
}

// HashPassword 加密密码
func (s *AuthService) HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return fmt.Sprintf("%x", hash)
}

// VerifyPassword 验证密码
func (s *AuthService) VerifyPassword(password, hashedPassword string) bool {
	return s.HashPassword(password) == hashedPassword
}

// Login 用户登录
func (s *AuthService) Login(username, password string) (*models.User, error) {
	// 根据用户名查找用户
	user, err := s.userService.GetUserByUsername(username)
	if err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 检查用户状态
	if !user.CanLogin() {
		switch user.Status {
		case models.StatusPending:
			return nil, fmt.Errorf("账户正在审核中，请等待管理员审核")
		case models.StatusRejected:
			return nil, fmt.Errorf("账户申请已被拒绝，请联系管理员")
		case models.StatusDisabled:
			return nil, fmt.Errorf("账户已被禁用，请联系管理员")
		default:
			return nil, fmt.Errorf("账户状态异常，请联系管理员")
		}
	}

	// 验证密码
	if !s.VerifyPassword(password, user.Password) {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 更新登录信息
	now := time.Now()
	user.LastLoginAt = &now
	user.LoginCount++

	if err := s.db.Save(user).Error; err != nil {
		// 登录信息更新失败不影响登录流程
		fmt.Printf("更新登录信息失败: %v\n", err)
	}

	return user, nil
}

// CreateUserWithPassword 创建带密码的用户
func (s *AuthService) CreateUserWithPassword(req models.UserCreateRequest) (*models.User, error) {
	// 检查用户名和邮箱是否已存在
	exists, err := s.userService.CheckUserExists(req.Username, req.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		// 进一步检查是否是活跃用户
		activeExists, err := s.userService.CheckActiveUserExists(req.Username, req.Email)
		if err != nil {
			return nil, err
		}
		if activeExists {
			return nil, fmt.Errorf("用户名或邮箱已存在")
		} else {
			return nil, fmt.Errorf("用户名或邮箱已存在（可能是已停用的用户，请联系管理员重新激活）")
		}
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: s.HashPassword(req.Password), // 加密密码
		FullName: req.FullName,
		Role:     req.Role,
		Avatar:   req.Avatar,
		Status:   models.StatusPending, // 新注册用户默认为待审核状态
		IsActive: false,                // 新注册用户默认未激活，需要审核
	}

	// 设置默认角色
	if user.Role == "" {
		user.Role = models.RoleDeveloper
	}

	err = s.db.Create(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// CreateUserByAdmin 管理员创建用户（直接激活）
func (s *AuthService) CreateUserByAdmin(req models.UserCreateRequest) (*models.User, error) {
	// 检查用户名和邮箱是否已存在
	exists, err := s.userService.CheckUserExists(req.Username, req.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, fmt.Errorf("用户名或邮箱已存在")
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: s.HashPassword(req.Password), // 加密密码
		FullName: req.FullName,
		Role:     req.Role,
		Avatar:   req.Avatar,
		Status:   models.StatusActive, // 管理员创建的用户直接激活
		IsActive: true,
	}

	// 设置默认角色
	if user.Role == "" {
		user.Role = models.RoleDeveloper
	}

	err = s.db.Create(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID, oldPassword, newPassword string) error {
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 验证旧密码
	if !s.VerifyPassword(oldPassword, user.Password) {
		return fmt.Errorf("原密码错误")
	}

	// 更新密码
	user.Password = s.HashPassword(newPassword)
	return s.db.Save(user).Error
}

// ValidatePassword 验证密码强度
func (s *AuthService) ValidatePassword(password string) error {
	if len(password) < 6 {
		return fmt.Errorf("密码长度至少6位")
	}
	if len(password) > 100 {
		return fmt.Errorf("密码长度不能超过100位")
	}
	return nil
}
