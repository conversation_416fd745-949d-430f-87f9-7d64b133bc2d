package models

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AttachmentType 附件类型枚举
type AttachmentType string

const (
	AttachmentTypeImage    AttachmentType = "image"
	AttachmentTypeDocument AttachmentType = "document"
	AttachmentTypeVideo    AttachmentType = "video"
	AttachmentTypeAudio    AttachmentType = "audio"
	AttachmentTypeOther    AttachmentType = "other"
)

// Attachment 附件模型
type Attachment struct {
	ID           string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	TaskID       string         `json:"task_id" gorm:"not null;type:varchar(36);index"`
	FileName     string         `json:"file_name" gorm:"not null;size:255"`
	OriginalName string         `json:"original_name" gorm:"not null;size:255"`
	FilePath     string         `json:"file_path" gorm:"not null;size:500"`
	FileSize     int64          `json:"file_size" gorm:"not null"`
	MimeType     string         `json:"mime_type" gorm:"not null;size:100"`
	Type         AttachmentType `json:"type" gorm:"not null"`
	UploadedBy   string         `json:"uploaded_by" gorm:"type:varchar(36)"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Task *Task `json:"task,omitempty" gorm:"foreignKey:TaskID"`
}

// BeforeCreate 创建前钩子
func (a *Attachment) BeforeCreate(tx *gorm.DB) error {
	if a.ID == "" {
		a.ID = uuid.New().String()
	}
	return nil
}

// GetDisplaySize 获取文件大小的可读格式
func (a *Attachment) GetDisplaySize() string {
	size := float64(a.FileSize)
	units := []string{"B", "KB", "MB", "GB", "TB"}

	for i, unit := range units {
		if size < 1024 || i == len(units)-1 {
			if i == 0 {
				return fmt.Sprintf("%.0f %s", size, unit)
			}
			return fmt.Sprintf("%.1f %s", size, unit)
		}
		size /= 1024
	}
	return fmt.Sprintf("%.1f %s", size, units[len(units)-1])
}

// IsImage 判断是否为图片
func (a *Attachment) IsImage() bool {
	return a.Type == AttachmentTypeImage
}

// GetTypeIcon 获取文件类型图标
func (a *Attachment) GetTypeIcon() string {
	switch a.Type {
	case AttachmentTypeImage:
		return "🖼️"
	case AttachmentTypeDocument:
		return "📄"
	case AttachmentTypeVideo:
		return "🎥"
	case AttachmentTypeAudio:
		return "🎵"
	default:
		return "📎"
	}
}

// DetermineAttachmentType 根据MIME类型确定附件类型
func DetermineAttachmentType(mimeType string) AttachmentType {
	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return AttachmentTypeImage
	case strings.HasPrefix(mimeType, "video/"):
		return AttachmentTypeVideo
	case strings.HasPrefix(mimeType, "audio/"):
		return AttachmentTypeAudio
	case mimeType == "application/pdf" ||
		strings.HasPrefix(mimeType, "application/msword") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument") ||
		mimeType == "text/plain":
		return AttachmentTypeDocument
	default:
		return AttachmentTypeOther
	}
}

// AttachmentCreateRequest 创建附件请求结构
type AttachmentCreateRequest struct {
	TaskID       string `json:"task_id" binding:"required"`
	FileName     string `json:"file_name" binding:"required"`
	OriginalName string `json:"original_name" binding:"required"`
	FilePath     string `json:"file_path" binding:"required"`
	FileSize     int64  `json:"file_size" binding:"required"`
	MimeType     string `json:"mime_type" binding:"required"`
	UploadedBy   string `json:"uploaded_by"`
}
