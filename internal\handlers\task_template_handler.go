package handlers

import (
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
)

// TaskTemplateHandler 任务模板处理器
type TaskTemplateHandler struct {
	taskTemplateService *services.TaskTemplateService
}

// NewTaskTemplateHandler 创建新的任务模板处理器
func NewTaskTemplateHandler(taskTemplateService *services.TaskTemplateService) *TaskTemplateHandler {
	return &TaskTemplateHandler{
		taskTemplateService: taskTemplateService,
	}
}

// GetTaskTemplates 获取所有任务模板API
func (h *TaskTemplateHandler) GetTaskTemplates(c *gin.Context) {
	templates, err := h.taskTemplateService.GetAllTaskTemplates()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取模板列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"success":   true,
		"message":   "获取模板列表成功",
		"templates": templates,
	})
}

// GetTaskTemplateByID 根据ID获取任务模板API
func (h *TaskTemplateHandler) GetTaskTemplateByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "模板ID不能为空",
		})
		return
	}

	template, err := h.taskTemplateService.GetTaskTemplateByID(id)
	if err != nil {
		if err.Error() == "模板不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取模板失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "获取模板成功",
		"template": template,
	})
}

// CreateTaskTemplate 创建任务模板API
func (h *TaskTemplateHandler) CreateTaskTemplate(c *gin.Context) {
	var req models.TaskTemplateCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 从认证中间件获取当前用户信息
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	user := userInterface.(*models.User)
	template, err := h.taskTemplateService.CreateTaskTemplate(req, user.ID)
	if err != nil {
		if err.Error() == "模板名称已存在" {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建模板失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success":  true,
		"message":  "模板创建成功",
		"template": template,
	})
}

// UpdateTaskTemplate 更新任务模板API
func (h *TaskTemplateHandler) UpdateTaskTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "模板ID不能为空",
		})
		return
	}

	var req models.TaskTemplateUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	template, err := h.taskTemplateService.UpdateTaskTemplate(id, req)
	if err != nil {
		if err.Error() == "模板不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		if err.Error() == "模板名称已存在" {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新模板失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "模板更新成功",
		"template": template,
	})
}

// DeleteTaskTemplate 删除任务模板API
func (h *TaskTemplateHandler) DeleteTaskTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "模板ID不能为空",
		})
		return
	}

	err := h.taskTemplateService.DeleteTaskTemplate(id)
	if err != nil {
		if err.Error() == "模板不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		if err.Error() == "系统默认模板不能删除" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除模板失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "模板删除成功",
	})
}

// GetTaskTemplateStats 获取模板统计信息API
func (h *TaskTemplateHandler) GetTaskTemplateStats(c *gin.Context) {
	stats, err := h.taskTemplateService.GetTaskTemplateStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取统计信息成功",
		"stats":   stats,
	})
}
