package middleware

import (
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	sessionService *services.SessionService
}

// NewAuthMiddleware 创建新的认证中间件实例
func NewAuthMiddleware(sessionService *services.SessionService) *AuthMiddleware {
	return &AuthMiddleware{
		sessionService: sessionService,
	}
}

// RequireAuth 需要认证的中间件
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Cookie中获取会话ID
		sessionID, err := c.<PERSON><PERSON>("session_id")
		if err != nil {
			// 如果是API请求，返回JSON错误
			if isAPIRequest(c) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"message": "请先登录",
				})
				c.Abort()
				return
			}
			// 如果是页面请求，重定向到登录页
			c.Redirect(http.StatusFound, "/login")
			c.Abort()
			return
		}

		// 验证会话
		session, err := m.sessionService.GetSession(sessionID)
		if err != nil {
			// 清除无效的Cookie
			c.SetCookie("session_id", "", -1, "/", "", false, true)

			if isAPIRequest(c) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"message": "会话已过期，请重新登录",
				})
				c.Abort()
				return
			}
			c.Redirect(http.StatusFound, "/login")
			c.Abort()
			return
		}

		// 刷新会话
		m.sessionService.RefreshSession(sessionID)

		// 将用户信息存储到上下文中
		c.Set("user", session.User)
		c.Set("session", session)

		c.Next()
	}
}

// OptionalAuth 可选认证的中间件
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Cookie中获取会话ID
		sessionID, err := c.Cookie("session_id")
		if err != nil {
			c.Next()
			return
		}

		// 验证会话
		session, err := m.sessionService.GetSession(sessionID)
		if err != nil {
			// 清除无效的Cookie
			c.SetCookie("session_id", "", -1, "/", "", false, true)
			c.Next()
			return
		}

		// 刷新会话
		m.sessionService.RefreshSession(sessionID)

		// 将用户信息存储到上下文中
		c.Set("user", session.User)
		c.Set("session", session)

		c.Next()
	}
}

// RequireRole 需要特定角色的中间件
func (m *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			if isAPIRequest(c) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"message": "请先登录",
				})
				c.Abort()
				return
			}
			c.Redirect(http.StatusFound, "/login")
			c.Abort()
			return
		}

		// 从上下文中获取的是 *models.User 类型
		userObj := user.(*models.User)
		userRole := string(userObj.Role)

		// 检查用户角色
		hasRole := false
		for _, role := range roles {
			if userRole == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			if isAPIRequest(c) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "权限不足",
				})
				c.Abort()
				return
			}
			c.HTML(http.StatusForbidden, "error.html", gin.H{
				"error": "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// isAPIRequest 判断是否为API请求
func isAPIRequest(c *gin.Context) bool {
	path := c.Request.URL.Path
	return c.Request.Header.Get("Content-Type") == "application/json" ||
		c.Request.Header.Get("Accept") == "application/json" ||
		(len(path) >= 4 && path[:4] == "/api")
}

// GetCurrentUser 从上下文中获取当前用户
func GetCurrentUser(c *gin.Context) (*models.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	// 从上下文中获取的是 *models.User 类型
	userObj := user.(*models.User)
	return userObj, true
}
