<!DOCTYPE html>
<html>
<head>
    <title>测试用户API</title>
</head>
<body>
    <h1>用户API测试</h1>
    <button onclick="testUserAPI()">测试用户API</button>
    <pre id="result"></pre>

    <script>
        async function testUserAPI() {
            try {
                const response = await fetch('/api/users');
                const data = await response.json();
                
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
                
                // 查找xiaochun用户
                if (data.users) {
                    const xiaochunUser = data.users.find(user => user.username === 'xiaochun');
                    if (xiaochunUser) {
                        console.log('xiaochun用户数据:', xiaochunUser);
                        console.log('角色:', xiaochunUser.role);
                    }
                }
            } catch (error) {
                console.error('API调用失败:', error);
                document.getElementById('result').textContent = 'API调用失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
